日志：
行 12954: 2025-08-29 14:31:10,969 [ 503050]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY_AUDIT [Session: noauth_1756448620340_8c5618b] [Op: 1/1000] GET_AUTH_INFO_OPTIONAL - Returning restricted auth info
行 12955: 2025-08-29 14:31:10,969 [ 503050]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY_AUDIT [Session: noauth_1756448620340_8c5618b] [Op: 2/1000] CHECK_AUTHENTICATION - Returning development authentication status
行 12956: 2025-08-29 14:31:10,977 [ 503058]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY_AUDIT [Session: noauth_1756448620340_8c5618b] [Op: 3/1000] GET_AUTH_INFO_OPTIONAL - Returning restricted auth info
行 12957: 2025-08-29 14:31:10,977 [ 503058]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY_AUDIT [Session: noauth_1756448620340_8c5618b] [Op: 4/1000] CHECK_AUTHENTICATION - Returning development authentication status
行 12958: 2025-08-29 14:31:33,781 [ 525862]   INFO - #c.i.i.p.DynamicPlugins - Plugin ai.zencoder.plugin.noauth is not unload-safe because of unresolved extension com.intellij.projectActivity
行 12974: 2025-08-29 14:31:42,160 [ 534241]   INFO - #ai.zencoder.plugin.transport.mcp.McpAblyTransport - Disposing MCP Ably transport
行 12975: 2025-08-29 14:31:42,160 [ 534241]   INFO - #ai.zencoder.plugin.socket.ably.AblyConnectionManager - Disposing Ably connection manager
行 12987: 2025-08-29 14:31:42,167 [ 534248]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - MCP MessageBus server closed
行 12988: 2025-08-29 14:31:42,169 [ 534250]   INFO - #ai.zencoder.plugin.webview.WebviewWrapper - Disposing webview
行 12989: 2025-08-29 14:31:42,170 [ 534251]   INFO - #ai.zencoder.plugin.providers.completion.nextchange.DeltaBasedHistoryService - Disposing DeltaBasedHistoryService for project: augment-plugin
行 12990: 2025-08-29 14:31:42,170 [ 534251]   INFO - #ai.zencoder.plugin.providers.completion.nextchange.ThreadSafeDocumentChangeTracker - Disposing DocumentChangeTracker
行 13330: 2025-08-29 14:31:58,187 [   3216]   INFO - #ai.zencoder.plugin.providers.completion.nextchange.DeltaBasedHistoryService - DeltaBasedHistoryService initialized for project: augment-plugin
行 13358: 2025-08-29 14:32:05,213 [  10242]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 13364: 2025-08-29 14:32:05,247 [  10276]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 13365: 2025-08-29 14:32:05,262 [  10291]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 13366: 2025-08-29 14:32:05,262 [  10291]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 13367: 2025-08-29 14:32:05,277 [  10306]   INFO - #ai.zencoder.plugin.chat.agentmodels.AgentModelsManager - Fetching agent models config
行 13374: 2025-08-29 14:32:05,826 [  10855]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 13375: 2025-08-29 14:32:05,828 [  10857]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 13376: 2025-08-29 14:32:05,828 [  10857]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 13377: 2025-08-29 14:32:05,828 [  10857]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 13378: 2025-08-29 14:32:05,828 [  10857]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 13379: 2025-08-29 14:32:05,834 [  10863]   INFO - #ai.zencoder.plugin.flags.FeatureFlagsService - Refreshing feature flags
行 13380: 2025-08-29 14:32:05,834 [  10863]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 13458: 2025-08-29 14:32:06,852 [  11881]   INFO - #ai.zencoder.plugin.flags.FeatureFlagsService - New flags: {enable-web-dev-agent=FeatureFlagVariant(value=on, payload=null), use-zencoder-cli-agent-runtime=FeatureFlagVariant(value=null, payload=null), enable-new-chat-manager=FeatureFlagVariant(value=on, payload=null), enable-private-deployment-mvp=FeatureFlagVariant(value=null, payload=null), plugin-banners=FeatureFlagVariant(value=on, payload=[{"button":{"label":"Learn more","type":"external_link","url":"https://docs.zencoder.ai/features/web-dev-agent"},"endDate":"2025-08-26T00:00:00Z","icon":{"attributes":{"fill":"none","height":"13","viewBox":"0 0 14 13","width":"14","xmlns":"http://www.w3.org/2000/svg"},"children":[{"attributes":{"d":"M12.407 0.590089C12.3615 0.39829 12.2423 0.232113 12.0753 0.127506C11.9082 0.0228982 11.7066 -0.0117285 11.5142 0.0311165C11.3218 0.0739615 11.154 0.190833 11.0471 0.356449C10.9402 0.522065 10.9028 0.723109 10.943 0.916089C11.308 2.55209 11.5 4.25309 11.5 6.00009C11.5 7.74709 11.308 9.44809 10.943 11.0841C10.9215 11.1802 10.9192 11.2796 10.9362 11.3767C10.9531 11.4737 10.989 11.5664 11.0418 11.6496C11.0946 11.7327 11.1632 11.8047 11.2438 11.8613C11.3244 11.9179 11.4154 11.9581 11.5115 11.9796C11.6076 12.0011 11.707 12.0034 11.8041 11.9864C11.9011 11.9695 11.9938 11.9336 12.077 11.8808C12.1601 11.828 12.2321 11.7594 12.2887 11.6788C12.3453 11.5982 12.3855 11.5072 12.407 11.4111C12.671 10.2261 12.851 9.00909 12.938 7.76709C13.259 7.5969 13.5276 7.34249 13.7149 7.03118C13.9022 6.71986 14.0011 6.36341 14.0011 6.00009C14.0011 5.63677 13.9022 5.28032 13.7149 4.969C13.5276 4.65769 13.259 4.40328 12.938 4.23309C12.8517 3.00725 12.6742 1.78955 12.407 0.590089ZM3.348 9.00009H3C2.20435 9.00009 1.44129 8.68402 0.87868 8.12141C0.31607 7.5588 0 6.79574 0 6.00009C0 5.20444 0.31607 4.44138 0.87868 3.87877C1.44129 3.31616 2.20435 3.00009 3 3.00009H5C6.647 3.00009 8.217 2.66809 9.646 2.06709C9.878 3.34109 10 4.65509 10 6.00009C10 7.34509 9.878 8.65909 9.646 9.93309C8.30412 9.36966 6.8 ...
行 13487: 2025-08-29 14:32:07,413 [  12442]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 13490: 2025-08-29 14:32:07,439 [  12468]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - Starting MCP MessageBus server...
行 13491: 2025-08-29 14:32:07,440 [  12469]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 13492: 2025-08-29 14:32:07,446 [  12475]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 13493: 2025-08-29 14:32:07,462 [  12491]   INFO - #ai.zencoder.plugin.agents.CustomAgentsManager - Custom agents manager has been loaded
行 13494: 2025-08-29 14:32:07,462 [  12491]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 13498: 2025-08-29 14:32:07,491 [  12520]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 13513: 2025-08-29 14:32:07,611 [  12640]   INFO - STDOUT - 14:32:07,563 |-INFO in ch.qos.logback.core.model.processor.ConversionRuleModelHandler - registering conversion word tid with class [ai.zencoder.plugin.logging.ThreadIdConverter]
行 13548: 2025-08-29 14:32:07,663 [  12692]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registering annotated MCP handlers...
行 13549: 2025-08-29 14:32:07,663 [  12692]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Running on Windows - using enhanced classpath scanning configuration
行 13550: 2025-08-29 14:32:07,663 [  12692]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Scanning for classes annotated with @Service
行 13565: 2025-08-29 14:32:08,407 [  13436]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Found 109 classes annotated with @Service
行 13566: 2025-08-29 14:32:08,408 [  13437]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Scanning for classes annotated with @McpTool
行 13568: 2025-08-29 14:32:08,853 [  13882]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Found 0 classes annotated with @McpTool
行 13569: 2025-08-29 14:32:08,853 [  13882]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Adding explicitly registered service classes
行 13570: 2025-08-29 14:32:08,853 [  13882]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Added 0 explicitly registered service classes
行 13571: 2025-08-29 14:32:08,853 [  13882]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Found 109 total service classes:
行 13589: 2025-08-29 14:32:09,380 [  14409]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: list_resources
行 13591: 2025-08-29 14:32:09,382 [  14411]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: read_resource
行 13595: 2025-08-29 14:32:10,038 [  15067]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: file_search
行 13598: 2025-08-29 14:32:10,051 [  15080]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: fulltext_search
行 13600: 2025-08-29 14:32:10,208 [  15237]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Annotated MCP handlers registered successfully
行 13601: 2025-08-29 14:32:10,208 [  15237]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - MCP MessageBus server started successfully
行 13602: 2025-08-29 14:32:10,282 [  15311]   WARN - #ai.zencoder.plugin.chat.agentmodels.AgentModelsManager - Using default models configuration due to fetch failure
行 13874: 2025-08-29 14:32:28,462 [  33491]   INFO - #c.i.i.p.DynamicPlugins - Plugin ai.zencoder.plugin.noauth is not unload-safe because of unresolved extension com.intellij.projectActivity
行 13910: 2025-08-29 14:32:45,056 [  50085]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - MCP MessageBus server closed
行 13911: 2025-08-29 14:32:45,061 [  50090]   INFO - #ai.zencoder.plugin.webview.WebviewWrapper - Disposing webview
行 13912: 2025-08-29 14:32:45,061 [  50090]   INFO - #ai.zencoder.plugin.providers.completion.nextchange.DeltaBasedHistoryService - Disposing DeltaBasedHistoryService for project: augment-plugin
行 13913: 2025-08-29 14:32:45,062 [  50091]   INFO - #ai.zencoder.plugin.providers.completion.nextchange.ThreadSafeDocumentChangeTracker - Disposing DocumentChangeTracker
行 14254: 2025-08-29 14:32:51,519 [   3040]   INFO - #ai.zencoder.plugin.providers.completion.nextchange.DeltaBasedHistoryService - DeltaBasedHistoryService initialized for project: augment-plugin
行 14285: 2025-08-29 14:32:53,170 [   4691]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 14286: 2025-08-29 14:32:53,201 [   4722]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 14287: 2025-08-29 14:32:53,217 [   4738]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 14288: 2025-08-29 14:32:53,218 [   4739]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 14290: 2025-08-29 14:32:53,236 [   4757]   INFO - #ai.zencoder.plugin.chat.agentmodels.AgentModelsManager - Fetching agent models config
行 14293: 2025-08-29 14:32:53,854 [   5375]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 14294: 2025-08-29 14:32:53,854 [   5375]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 14295: 2025-08-29 14:32:53,854 [   5375]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 14296: 2025-08-29 14:32:53,854 [   5375]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 14297: 2025-08-29 14:32:53,854 [   5375]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 14298: 2025-08-29 14:32:53,860 [   5381]   INFO - #ai.zencoder.plugin.flags.FeatureFlagsService - Refreshing feature flags
行 14299: 2025-08-29 14:32:53,860 [   5381]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 14303: 2025-08-29 14:32:54,286 [   5807]   INFO - #ai.zencoder.plugin.chat.agentmodels.AgentModelsManager - Successfully fetched models config with 9 models
行 14304: 2025-08-29 14:32:54,500 [   6021]   INFO - #ai.zencoder.plugin.flags.FeatureFlagsService - New flags: {enable-web-dev-agent=FeatureFlagVariant(value=on, payload=null), use-zencoder-cli-agent-runtime=FeatureFlagVariant(value=null, payload=null), enable-new-chat-manager=FeatureFlagVariant(value=on, payload=null), enable-private-deployment-mvp=FeatureFlagVariant(value=null, payload=null), plugin-banners=FeatureFlagVariant(value=on, payload=[{"button":{"label":"Learn more","type":"external_link","url":"https://docs.zencoder.ai/features/web-dev-agent"},"endDate":"2025-08-26T00:00:00Z","icon":{"attributes":{"fill":"none","height":"13","viewBox":"0 0 14 13","width":"14","xmlns":"http://www.w3.org/2000/svg"},"children":[{"attributes":{"d":"M12.407 0.590089C12.3615 0.39829 12.2423 0.232113 12.0753 0.127506C11.9082 0.0228982 11.7066 -0.0117285 11.5142 0.0311165C11.3218 0.0739615 11.154 0.190833 11.0471 0.356449C10.9402 0.522065 10.9028 0.723109 10.943 0.916089C11.308 2.55209 11.5 4.25309 11.5 6.00009C11.5 7.74709 11.308 9.44809 10.943 11.0841C10.9215 11.1802 10.9192 11.2796 10.9362 11.3767C10.9531 11.4737 10.989 11.5664 11.0418 11.6496C11.0946 11.7327 11.1632 11.8047 11.2438 11.8613C11.3244 11.9179 11.4154 11.9581 11.5115 11.9796C11.6076 12.0011 11.707 12.0034 11.8041 11.9864C11.9011 11.9695 11.9938 11.9336 12.077 11.8808C12.1601 11.828 12.2321 11.7594 12.2887 11.6788C12.3453 11.5982 12.3855 11.5072 12.407 11.4111C12.671 10.2261 12.851 9.00909 12.938 7.76709C13.259 7.5969 13.5276 7.34249 13.7149 7.03118C13.9022 6.71986 14.0011 6.36341 14.0011 6.00009C14.0011 5.63677 13.9022 5.28032 13.7149 4.969C13.5276 4.65769 13.259 4.40328 12.938 4.23309C12.8517 3.00725 12.6742 1.78955 12.407 0.590089ZM3.348 9.00009H3C2.20435 9.00009 1.44129 8.68402 0.87868 8.12141C0.31607 7.5588 0 6.79574 0 6.00009C0 5.20444 0.31607 4.44138 0.87868 3.87877C1.44129 3.31616 2.20435 3.00009 3 3.00009H5C6.647 3.00009 8.217 2.66809 9.646 2.06709C9.878 3.34109 10 4.65509 10 6.00009C10 7.34509 9.878 8.65909 9.646 9.93309C8.30412 9.36966 6.8 ...
行 14414: 2025-08-29 14:32:55,967 [   7488]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 14415: 2025-08-29 14:32:56,003 [   7524]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - Starting MCP MessageBus server...
行 14416: 2025-08-29 14:32:56,004 [   7525]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 14417: 2025-08-29 14:32:56,011 [   7532]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 14418: 2025-08-29 14:32:56,038 [   7559]   INFO - #ai.zencoder.plugin.agents.CustomAgentsManager - Custom agents manager has been loaded
行 14419: 2025-08-29 14:32:56,039 [   7560]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 14423: 2025-08-29 14:32:56,058 [   7579]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: null, refresh token: null
行 14443: 2025-08-29 14:32:56,224 [   7745]   INFO - STDOUT - 14:32:56,171 |-INFO in ch.qos.logback.core.model.processor.ConversionRuleModelHandler - registering conversion word tid with class [ai.zencoder.plugin.logging.ThreadIdConverter]
行 14478: 2025-08-29 14:32:56,310 [   7831]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registering annotated MCP handlers...
行 14479: 2025-08-29 14:32:56,310 [   7831]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Running on Windows - using enhanced classpath scanning configuration
行 14480: 2025-08-29 14:32:56,310 [   7831]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Scanning for classes annotated with @Service
行 14481: 2025-08-29 14:32:56,859 [   8380]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Found 109 classes annotated with @Service
行 14482: 2025-08-29 14:32:56,859 [   8380]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Scanning for classes annotated with @McpTool
行 14491: 2025-08-29 14:32:57,309 [   8830]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Found 0 classes annotated with @McpTool
行 14492: 2025-08-29 14:32:57,309 [   8830]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Adding explicitly registered service classes
行 14493: 2025-08-29 14:32:57,310 [   8831]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Added 0 explicitly registered service classes
行 14494: 2025-08-29 14:32:57,310 [   8831]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Found 109 total service classes:
行 14504: 2025-08-29 14:32:57,855 [   9376]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: list_resources
行 14506: 2025-08-29 14:32:57,858 [   9379]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: read_resource
行 14510: 2025-08-29 14:32:58,260 [   9781]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: file_search
行 14513: 2025-08-29 14:32:58,273 [   9794]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: fulltext_search
行 14514: 2025-08-29 14:32:58,395 [   9916]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Annotated MCP handlers registered successfully
行 14515: 2025-08-29 14:32:58,395 [   9916]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - MCP MessageBus server started successfully
行 14855: 	at ai.zencoder.plugin.bypass.ZencoderStateSynchronizer.<clinit>(ZencoderStateSynchronizer.java:39)
行 14856: 	at ai.zencoder.plugin.action.ToggleNoAuthAction.update(ToggleNoAuthAction.java:94)
行 14950: 2025-08-29 14:33:18,077 [  29598] SEVERE - #c.i.s.ComponentManagerImpl - ai.zencoder.plugin.bypass.ZencoderStateSynchronizer <clinit> requests com.intellij.ide.plugins.PluginUtil instance. Class initialization must not depend on services. Consider using instance of the service on-demand instead.
行 14951: java.lang.Throwable: ai.zencoder.plugin.bypass.ZencoderStateSynchronizer <clinit> requests com.intellij.ide.plugins.PluginUtil instance. Class initialization must not depend on services. Consider using instance of the service on-demand instead.
行 14964: 	at ai.zencoder.plugin.bypass.ZencoderStateSynchronizer.<clinit>(ZencoderStateSynchronizer.java:39)
行 14965: 	at ai.zencoder.plugin.action.ToggleNoAuthAction.update(ToggleNoAuthAction.java:94)
行 15065: 	at ai.zencoder.plugin.bypass.ZencoderStateSynchronizer.<clinit>(ZencoderStateSynchronizer.java:40)
行 15066: 	at ai.zencoder.plugin.action.ToggleNoAuthAction.update(ToggleNoAuthAction.java:94)
行 15160: 2025-08-29 14:33:18,084 [  29605]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - Development mode not explicitly enabled, but proceeding
行 15161: 2025-08-29 14:33:18,084 [  29605]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SecureNoAuthService initialized - Session: noauth_1756449198084_3f93c193 at 2025-08-29T14:33:18.0840421
行 15162: 2025-08-29 14:33:18,084 [  29605]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - ================================================================================
行 15163: 2025-08-29 14:33:18,084 [  29605]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY WARNING: No-Auth mode is ACTIVE
行 15164: 2025-08-29 14:33:18,084 [  29605]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - This bypasses normal authentication and should ONLY be used for development!
行 15165: 2025-08-29 14:33:18,084 [  29605]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - Session ID: noauth_1756449198084_3f93c193
行 15166: 2025-08-29 14:33:18,084 [  29605]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - Started at: 2025-08-29T14:33:18.0840421
行 15167: 2025-08-29 14:33:18,084 [  29605]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - ================================================================================
行 15168: 2025-08-29 14:33:18,084 [  29605]   INFO - #ai.zencoder.plugin.noauth.AuthServiceReplacer - Successfully initialized SecureNoAuthService
行 15170: 2025-08-29 14:33:29,982 [  41503]   INFO - #ai.zencoder.plugin.action.DiagnosticAction - Running Zencoder diagnostic...
行 15171: 2025-08-29 14:33:30,989 [  42510]   INFO - #ai.zencoder.plugin.settings.SafeZencoderSettingsManager - Attempting to safely initialize Zencoder settings
行 15179: 2025-08-29 14:33:31,992 [  43513]   INFO - #ai.zencoder.plugin.settings.SafeZencoderSettingsManager - Successfully initialized Zencoder settings

页面效果：
![img_1.png](img_1.png) 没有自动绕过登录

