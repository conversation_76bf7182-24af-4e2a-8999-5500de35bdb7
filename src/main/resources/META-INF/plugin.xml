<?xml version="1.0" encoding="UTF-8"?>
<idea-plugin>
    <id>ai.zencoder.plugin.noauth</id>
    <name>Zencoder No-Auth Extension</name>
    <version>1.0.0</version>
    <vendor email="<EMAIL>" url="https://your-website.com">Your Company</vendor>
    
    <description><![CDATA[
    <h1>Zencoder No-Auth Extension</h1>
    <p>This plugin extends Zencoder to work without authentication, suitable for development and testing environments.</p>
    
    <h2>Features</h2>
    <ul>
        <li>Bypasses OAuth authentication flow</li>
        <li>Provides mock user credentials</li>
        <li>Enables all Zencoder features without login</li>
        <li>Suitable for offline development</li>
    </ul>
    
    <h2>Usage</h2>
    <p>Install this plugin alongside Zencoder. The authentication will be automatically bypassed.</p>
    
    <h2>Warning</h2>
    <p>This plugin is intended for development and testing purposes only. Do not use in production environments.</p>
    ]]></description>
    
    <!-- Plugin dependencies -->
    <depends optional="false">ai.zencoder.plugin</depends>
    
    <!-- Compatible IDE versions -->
    <idea-version since-build="233" until-build="243.*"/>
    
    <!-- Extension points -->
    <extensions defaultExtensionNs="com.intellij">
        <!-- AuthObserver implementation -->
        <applicationService
            serviceInterface="ai.zencoder.plugin.observers.auth.AuthObserver"
            serviceImplementation="ai.zencoder.plugin.observers.auth.AuthObserverImpl"/>

        <!-- Secure configuration manager -->
        <applicationService
            serviceImplementation="ai.zencoder.plugin.noauth.SecureNoAuthConfigManager"/>

        <!-- AuthService replacer -->
        <applicationService
            serviceImplementation="ai.zencoder.plugin.noauth.AuthServiceReplacer"/>

        <!-- Settings page -->
        <projectConfigurable
            id="ai.zencoder.plugin.noauth.settings"
            groupId="tools"
            instance="ai.zencoder.plugin.noauth.NoAuthSettingsConfigurable"
            displayName="Zencoder No-Auth"
            nonDefaultProject="false"/>

        <!-- Startup activity -->
        <projectActivity implementation="ai.zencoder.plugin.startup.NoAuthStartupActivity"/>

        <!-- Notification group -->
        <notificationGroup id="Zencoder.NoAuth"
                          displayType="BALLOON"
                          key="notification.group.zencoder.noauth"/>

        <!-- Safe Chat Tool Window -->
        <toolWindow id="ZencoderSafeChat"
                   factoryClass="ai.zencoder.plugin.ui.SafeChatToolWindowFactory"
                   anchor="right"
                   icon="/icons/zencoder.png"
                   canCloseContents="false"/>
    </extensions>
    
    <!-- 操作定义 -->
    <actions>
        <action id="zencoder.noauth.toggle"
                class="ai.zencoder.plugin.action.ToggleNoAuthAction"
                text="Toggle No-Auth Mode"
                description="Toggle Zencoder no-auth mode">
            <add-to-group group-id="ToolsMenu" anchor="last"/>
        </action>

        <action id="zencoder.diagnostic"
                class="ai.zencoder.plugin.action.DiagnosticAction"
                text="Zencoder Diagnostic"
                description="Run comprehensive Zencoder system diagnostic">
            <add-to-group group-id="ToolsMenu" anchor="last"/>
        </action>
    </actions>
</idea-plugin>
