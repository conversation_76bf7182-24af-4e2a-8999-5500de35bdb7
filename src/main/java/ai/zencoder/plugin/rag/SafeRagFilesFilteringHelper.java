package ai.zencoder.plugin.rag;

import ai.zencoder.plugin.network.NetworkTimeoutHandler;
import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;

/**
 * 安全的 RAG 文件过滤助手
 * 提供网络超时保护和离线模式支持
 */
public class SafeRagFilesFilteringHelper {
    
    private static final Logger LOG = Logger.getInstance(SafeRagFilesFilteringHelper.class);
    
    // 默认的文件过滤规则（离线模式）
    private static final Set<String> DEFAULT_EXCLUDED_EXTENSIONS = Set.of(
        ".log", ".tmp", ".cache", ".lock", ".pid", ".swp", ".bak",
        ".class", ".jar", ".war", ".ear", ".zip", ".tar", ".gz",
        ".exe", ".dll", ".so", ".dylib", ".bin", ".dat"
    );
    
    private static final Set<String> DEFAULT_EXCLUDED_DIRECTORIES = Set.of(
        "node_modules", ".git", ".svn", ".hg", "target", "build", 
        "dist", "out", ".idea", ".vscode", "coverage", "logs"
    );
    
    private static final Set<String> DEFAULT_INCLUDED_EXTENSIONS = Set.of(
        ".java", ".kt", ".js", ".ts", ".py", ".cpp", ".c", ".h",
        ".cs", ".go", ".rs", ".php", ".rb", ".swift", ".scala",
        ".md", ".txt", ".json", ".xml", ".yaml", ".yml", ".properties"
    );
    
    // 缓存的过滤规则
    private static volatile FilteringRules cachedRules = null;
    private static volatile long lastUpdateTime = 0;
    private static final long CACHE_DURATION_MS = 5 * 60 * 1000; // 5分钟缓存
    
    /**
     * 获取文件过滤规则，带网络超时保护
     */
    @NotNull
    public static FilteringRules getFilteringRules() {
        long currentTime = System.currentTimeMillis();
        
        // 检查缓存是否有效
        if (cachedRules != null && (currentTime - lastUpdateTime) < CACHE_DURATION_MS) {
            return cachedRules;
        }
        
        LOG.info("Attempting to fetch RAG filtering rules with timeout protection");
        
        // 尝试从网络获取规则，带超时保护
        FilteringRules networkRules = NetworkTimeoutHandler.executeWithTimeout(
            () -> fetchRulesFromNetwork(),
            3000, // 3秒超时
            null
        );
        
        if (networkRules != null) {
            LOG.info("Successfully fetched RAG filtering rules from network");
            cachedRules = networkRules;
            lastUpdateTime = currentTime;
            return networkRules;
        }
        
        // 网络获取失败，使用默认规则
        LOG.warn("Failed to fetch RAG filtering rules from network, using default offline rules");
        FilteringRules defaultRules = createDefaultRules();
        cachedRules = defaultRules;
        lastUpdateTime = currentTime;
        return defaultRules;
    }
    
    /**
     * 从网络获取过滤规则（可能抛出异常）
     */
    @Nullable
    private static FilteringRules fetchRulesFromNetwork() {
        try {
            // 这里模拟原始的网络获取逻辑
            // 实际实现中应该调用原始的 RagFilesFilteringHelper
            LOG.debug("Attempting to fetch rules from network...");
            
            // 如果网络不可用，直接返回 null
            if (!NetworkTimeoutHandler.isNetworkAvailable()) {
                LOG.debug("Network not available, skipping network fetch");
                return null;
            }
            
            // 这里应该是原始的网络获取逻辑
            // 由于我们无法直接修改原始类，这里返回 null 让它使用默认规则
            return null;
            
        } catch (Exception e) {
            LOG.warn("Exception while fetching rules from network", e);
            return null;
        }
    }
    
    /**
     * 创建默认的过滤规则
     */
    @NotNull
    private static FilteringRules createDefaultRules() {
        return new FilteringRules(
            new HashSet<>(DEFAULT_EXCLUDED_EXTENSIONS),
            new HashSet<>(DEFAULT_EXCLUDED_DIRECTORIES),
            new HashSet<>(DEFAULT_INCLUDED_EXTENSIONS),
            1024 * 1024, // 1MB 最大文件大小
            1000 // 最大文件数量
        );
    }
    
    /**
     * 检查文件是否应该被包含
     */
    public static boolean shouldIncludeFile(@NotNull String filePath) {
        FilteringRules rules = getFilteringRules();
        
        // 检查文件扩展名
        String extension = getFileExtension(filePath);
        if (extension != null) {
            if (rules.excludedExtensions.contains(extension.toLowerCase())) {
                return false;
            }
            if (!rules.includedExtensions.isEmpty() && 
                !rules.includedExtensions.contains(extension.toLowerCase())) {
                return false;
            }
        }
        
        // 检查目录
        for (String excludedDir : rules.excludedDirectories) {
            if (filePath.contains("/" + excludedDir + "/") || 
                filePath.contains("\\" + excludedDir + "\\")) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取文件扩展名
     */
    @Nullable
    private static String getFileExtension(@NotNull String filePath) {
        int lastDot = filePath.lastIndexOf('.');
        if (lastDot > 0 && lastDot < filePath.length() - 1) {
            return filePath.substring(lastDot);
        }
        return null;
    }
    
    /**
     * 过滤规则数据类
     */
    public static class FilteringRules {
        public final Set<String> excludedExtensions;
        public final Set<String> excludedDirectories;
        public final Set<String> includedExtensions;
        public final long maxFileSize;
        public final int maxFileCount;
        
        public FilteringRules(@NotNull Set<String> excludedExtensions,
                             @NotNull Set<String> excludedDirectories,
                             @NotNull Set<String> includedExtensions,
                             long maxFileSize,
                             int maxFileCount) {
            this.excludedExtensions = new HashSet<>(excludedExtensions);
            this.excludedDirectories = new HashSet<>(excludedDirectories);
            this.includedExtensions = new HashSet<>(includedExtensions);
            this.maxFileSize = maxFileSize;
            this.maxFileCount = maxFileCount;
        }
        
        @Override
        public String toString() {
            return "FilteringRules{" +
                   "excludedExtensions=" + excludedExtensions.size() + " items, " +
                   "excludedDirectories=" + excludedDirectories.size() + " items, " +
                   "includedExtensions=" + includedExtensions.size() + " items, " +
                   "maxFileSize=" + maxFileSize + ", " +
                   "maxFileCount=" + maxFileCount +
                   '}';
        }
    }
}
