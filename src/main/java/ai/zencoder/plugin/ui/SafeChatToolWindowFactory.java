package ai.zencoder.plugin.ui;

import ai.zencoder.plugin.network.NetworkTimeoutHandler;
import ai.zencoder.plugin.settings.SafeZencoderSettingsManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowFactory;
import com.intellij.ui.content.Content;
import com.intellij.ui.content.ContentFactory;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * 安全的聊天工具窗口工厂
 * 提供网络超时保护和离线模式支持
 */
public class SafeChatToolWindowFactory implements ToolWindowFactory {
    
    private static final Logger LOG = Logger.getInstance(SafeChatToolWindowFactory.class);
    
    @Override
    public void createToolWindowContent(@NotNull Project project, @NotNull ToolWindow toolWindow) {
        try {
            LOG.info("Creating safe chat tool window content");
            
            // 安全初始化设置
            SafeZencoderSettingsManager.safeInitialize();
            
            // 创建主面板
            JPanel mainPanel = createMainPanel(project);
            
            // 创建内容
            ContentFactory contentFactory = ContentFactory.getInstance();
            Content content = contentFactory.createContent(mainPanel, "", false);
            toolWindow.getContentManager().addContent(content);
            
            LOG.info("Successfully created safe chat tool window");
            
        } catch (Exception e) {
            LOG.error("Failed to create chat tool window, creating fallback UI", e);
            createFallbackUI(toolWindow, e);
        }
    }
    
    /**
     * 创建主面板
     */
    @NotNull
    private JPanel createMainPanel(@NotNull Project project) {
        JPanel panel = new JPanel(new BorderLayout());
        
        // 检查网络状态和设置状态
        boolean isOffline = SafeZencoderSettingsManager.isOfflineMode();
        boolean settingsOk = SafeZencoderSettingsManager.isInitialized();
        
        if (isOffline || !settingsOk) {
            // 离线模式或设置有问题，显示简化界面
            panel.add(createOfflinePanel(isOffline, settingsOk), BorderLayout.CENTER);
        } else {
            // 在线模式，尝试创建完整界面
            panel.add(createOnlinePanel(project), BorderLayout.CENTER);
        }
        
        // 添加状态栏
        panel.add(createStatusBar(), BorderLayout.SOUTH);
        
        return panel;
    }
    
    /**
     * 创建离线面板
     */
    @NotNull
    private JPanel createOfflinePanel(boolean isOffline, boolean settingsOk) {
        JPanel panel = new JPanel(new BorderLayout());
        
        // 标题
        JLabel titleLabel = new JLabel("Zencoder Chat", SwingConstants.CENTER);
        titleLabel.setFont(titleLabel.getFont().deriveFont(Font.BOLD, 16f));
        panel.add(titleLabel, BorderLayout.NORTH);
        
        // 主要内容区域
        JPanel contentPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        
        // 状态信息
        gbc.gridx = 0; gbc.gridy = 0;
        JLabel statusLabel = new JLabel();
        if (!settingsOk) {
            statusLabel.setText("<html><b>Settings initialization failed</b><br/>Using safe mode with default settings</html>");
            statusLabel.setForeground(Color.RED);
        } else if (isOffline) {
            statusLabel.setText("<html><b>Offline Mode</b><br/>Network connection not available</html>");
            statusLabel.setForeground(Color.ORANGE);
        }
        contentPanel.add(statusLabel, gbc);
        
        // 简单的聊天输入区域
        gbc.gridy = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        JTextArea inputArea = new JTextArea(3, 30);
        inputArea.setBorder(BorderFactory.createTitledBorder("Message (Offline Mode)"));
        inputArea.setEnabled(false);
        inputArea.setText("Chat functionality is currently unavailable due to network issues.\nPlease check your connection and restart the IDE.");
        contentPanel.add(new JScrollPane(inputArea), gbc);
        
        // 重试按钮
        gbc.gridy = 2;
        gbc.fill = GridBagConstraints.NONE;
        JButton retryButton = new JButton("Retry Connection");
        retryButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                retryConnection(retryButton);
            }
        });
        contentPanel.add(retryButton, gbc);
        
        panel.add(contentPanel, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 创建在线面板
     */
    @NotNull
    private JPanel createOnlinePanel(@NotNull Project project) {
        JPanel panel = new JPanel(new BorderLayout());
        
        // 标题
        JLabel titleLabel = new JLabel("Zencoder Chat - Online", SwingConstants.CENTER);
        titleLabel.setFont(titleLabel.getFont().deriveFont(Font.BOLD, 16f));
        titleLabel.setForeground(Color.GREEN);
        panel.add(titleLabel, BorderLayout.NORTH);
        
        // 聊天区域
        JTextArea chatArea = new JTextArea(15, 40);
        chatArea.setEditable(false);
        chatArea.setText("Welcome to Zencoder Chat!\n\nThis is a safe mode interface.\nThe full chat functionality will be available once the main Zencoder plugin is properly loaded.\n\nCurrent status: Authentication bypassed, waiting for UI components to initialize...");
        JScrollPane chatScroll = new JScrollPane(chatArea);
        chatScroll.setBorder(BorderFactory.createTitledBorder("Chat History"));
        panel.add(chatScroll, BorderLayout.CENTER);
        
        // 输入区域
        JPanel inputPanel = new JPanel(new BorderLayout());
        JTextArea inputArea = new JTextArea(2, 30);
        inputArea.setBorder(BorderFactory.createTitledBorder("Your Message"));
        JButton sendButton = new JButton("Send");
        sendButton.setEnabled(false); // 暂时禁用，直到完整功能可用
        
        inputPanel.add(new JScrollPane(inputArea), BorderLayout.CENTER);
        inputPanel.add(sendButton, BorderLayout.EAST);
        panel.add(inputPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    /**
     * 创建状态栏
     */
    @NotNull
    private JPanel createStatusBar() {
        JPanel statusBar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        statusBar.setBorder(BorderFactory.createLoweredBevelBorder());
        
        JLabel statusLabel = new JLabel();
        if (SafeZencoderSettingsManager.isOfflineMode()) {
            statusLabel.setText("Status: Offline Mode | " + SafeZencoderSettingsManager.getStatusInfo());
            statusLabel.setForeground(Color.ORANGE);
        } else {
            statusLabel.setText("Status: Online | " + SafeZencoderSettingsManager.getStatusInfo());
            statusLabel.setForeground(Color.GREEN);
        }
        
        statusBar.add(statusLabel);
        return statusBar;
    }
    
    /**
     * 重试连接
     */
    private void retryConnection(@NotNull JButton retryButton) {
        retryButton.setEnabled(false);
        retryButton.setText("Checking...");
        
        NetworkTimeoutHandler.executeWithTimeout(() -> {
            boolean networkAvailable = NetworkTimeoutHandler.isNetworkAvailable();
            
            SwingUtilities.invokeLater(() -> {
                if (networkAvailable) {
                    retryButton.setText("Connection OK");
                    retryButton.setForeground(Color.GREEN);
                    JOptionPane.showMessageDialog(
                        retryButton.getParent(),
                        "Network connection is now available!\nPlease restart the IDE to enable full functionality.",
                        "Connection Restored",
                        JOptionPane.INFORMATION_MESSAGE
                    );
                } else {
                    retryButton.setText("Still Offline");
                    retryButton.setForeground(Color.RED);
                    retryButton.setEnabled(true);
                }
            });
        }, 3000);
    }
    
    /**
     * 创建回退UI（当主要创建过程失败时）
     */
    private void createFallbackUI(@NotNull ToolWindow toolWindow, @NotNull Exception error) {
        try {
            JPanel fallbackPanel = new JPanel(new BorderLayout());
            
            JLabel errorLabel = new JLabel("<html><b>Chat Tool Window Error</b><br/>" +
                                          "Failed to initialize chat interface.<br/>" +
                                          "Error: " + error.getMessage() + "</html>");
            errorLabel.setForeground(Color.RED);
            errorLabel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
            
            fallbackPanel.add(errorLabel, BorderLayout.CENTER);
            
            ContentFactory contentFactory = ContentFactory.getInstance();
            Content content = contentFactory.createContent(fallbackPanel, "Error", false);
            toolWindow.getContentManager().addContent(content);
            
        } catch (Exception fallbackError) {
            LOG.error("Even fallback UI creation failed", fallbackError);
        }
    }
    
    @Override
    public boolean shouldBeAvailable(@NotNull Project project) {
        // 总是可用，即使在离线模式下也提供基本界面
        return true;
    }
}
