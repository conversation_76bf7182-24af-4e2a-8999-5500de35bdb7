package ai.zencoder.plugin.ui;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowManager;
import org.jetbrains.annotations.NotNull;

/**
 * Zencoder UI Activator
 * 负责激活和显示 Zencoder 主插件的 UI 组件
 */
public class ZencoderUIActivator {
    
    private static final Logger LOG = Logger.getInstance(ZencoderUIActivator.class);
    
    // Zencoder 可能的工具窗口 ID
    private static final String[] ZENCODER_TOOL_WINDOW_IDS = {
        "Zencoder",
        "ZencoderChat", 
        "Zencoder Chat",
        "AI Assistant",
        "Zencoder Assistant"
    };
    
    /**
     * 激活 Zencoder UI 组件
     */
    public static void activateZencoderUI(@NotNull Project project) {
        ApplicationManager.getApplication().invokeLater(() -> {
            try {
                LOG.info("Attempting to activate Zencoder UI components...");
                
                // 尝试激活工具窗口
                if (activateToolWindows(project)) {
                    LOG.info("Zencoder tool windows activated successfully");
                } else {
                    LOG.warn("No Zencoder tool windows found");
                }
                
                // 尝试触发 UI 初始化
                triggerUIInitialization(project);
                
            } catch (Exception e) {
                LOG.error("Failed to activate Zencoder UI", e);
            }
        });
    }
    
    /**
     * 激活工具窗口
     */
    private static boolean activateToolWindows(@NotNull Project project) {
        ToolWindowManager toolWindowManager = ToolWindowManager.getInstance(project);
        boolean activated = false;
        
        for (String windowId : ZENCODER_TOOL_WINDOW_IDS) {
            ToolWindow toolWindow = toolWindowManager.getToolWindow(windowId);
            if (toolWindow != null) {
                LOG.info("Found Zencoder tool window: " + windowId);
                toolWindow.activate(null);
                toolWindow.show(null);
                activated = true;
            }
        }
        
        return activated;
    }
    
    /**
     * 触发 UI 初始化
     */
    private static void triggerUIInitialization(@NotNull Project project) {
        try {
            // 尝试通过反射调用 Zencoder 的 UI 初始化方法
            Class<?> zencoderMainClass = findZencoderMainClass();
            if (zencoderMainClass != null) {
                LOG.info("Found Zencoder main class: " + zencoderMainClass.getName());
                // 这里可以添加具体的初始化调用
            }
            
        } catch (Exception e) {
            LOG.debug("Could not trigger UI initialization via reflection", e);
        }
    }
    
    /**
     * 查找 Zencoder 主类
     */
    private static Class<?> findZencoderMainClass() {
        String[] possibleClasses = {
            "ai.zencoder.plugin.ZencoderPlugin",
            "ai.zencoder.plugin.main.ZencoderMain",
            "ai.zencoder.plugin.ui.ZencoderUI",
            "ai.zencoder.plugin.chat.ChatService"
        };
        
        for (String className : possibleClasses) {
            try {
                return Class.forName(className);
            } catch (ClassNotFoundException ignored) {
                // 继续尝试下一个
            }
        }
        
        return null;
    }
}
