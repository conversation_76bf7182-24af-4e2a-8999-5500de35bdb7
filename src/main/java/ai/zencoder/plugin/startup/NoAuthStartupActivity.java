package ai.zencoder.plugin.startup;

import ai.zencoder.plugin.auth.AuthService;
import ai.zencoder.plugin.bypass.ZencoderBypassInitializer;
import ai.zencoder.plugin.noauth.AuthServiceReplacer;
import ai.zencoder.plugin.noauth.EnvironmentSecurityChecker;
import ai.zencoder.plugin.noauth.SecureNoAuthConfigManager;
import ai.zencoder.plugin.observers.auth.AuthObserver;
import ai.zencoder.plugin.observers.auth.AuthObserverUtils;
import ai.zencoder.plugin.ui.ZencoderUIActivator;
import ai.zencoder.plugin.settings.SafeZencoderSettingsManager;
import ai.zencoder.plugin.network.NetworkTimeoutHandler;
import com.intellij.notification.NotificationGroupManager;
import com.intellij.notification.NotificationType;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.ProjectActivity;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * 插件启动活动
 * 在插件启动时初始化免认证模式
 * 已迁移到 ProjectActivity 以符合新的 IntelliJ Platform API
 */
public class NoAuthStartupActivity implements ProjectActivity {
    
    private static final Logger LOG = Logger.getInstance(NoAuthStartupActivity.class);
    
    @Nullable
    @Override
    public Object execute(@NotNull Project project, @NotNull Continuation<? super Unit> continuation) {
        runActivity(project);
        return Unit.INSTANCE;
    }
    
    private void runActivity(@NotNull Project project) {
        LOG.info("Zencoder Secure No-Auth Extension starting...");
        
        try {
            // 执行环境安全检查
            EnvironmentSecurityChecker.SecurityCheckResult securityCheck =
                EnvironmentSecurityChecker.performSecurityCheck();
            
            if (!securityCheck.isSafe()) {
                LOG.error("Environment security check failed - No-Auth mode will not be activated");
                showSecurityErrorNotification(project, securityCheck);
                return;
            }
            
            // 获取配置管理器
            SecureNoAuthConfigManager configManager =
                ApplicationManager.getApplication().getService(SecureNoAuthConfigManager.class);
            
            // 预先初始化安全组件
            LOG.info("Initializing safe components to prevent network timeout issues...");
            SafeZencoderSettingsManager.safeInitialize();

            // 尝试启用No-Auth模式
            if (configManager.enableNoAuthMode("Plugin startup")) {
                LOG.warn("Secure No-Auth mode activated");

                // 启动完整的Zencoder绕过系统
                LOG.info("Starting comprehensive Zencoder bypass system...");
                ZencoderBypassInitializer bypassInitializer = new ZencoderBypassInitializer();
                bypassInitializer.execute(project, null);

                // 使用AuthServiceReplacer来避免服务冲突（保持向后兼容）
                AuthServiceReplacer authServiceReplacer =
                    ApplicationManager.getApplication().getService(AuthServiceReplacer.class);

                if (authServiceReplacer != null) {
                    AuthService authService = authServiceReplacer.getAuthService();
                    LOG.info("No-Auth service is active: " + authService.getClass().getSimpleName());

                    // 使用替换器提供的服务实例
                    performAuthenticationSetup(authService);

                    // 激活 Zencoder UI 组件
                    LOG.info("Activating Zencoder UI components...");
                    ZencoderUIActivator.activateZencoderUI(project);
                } else {
                    LOG.error("AuthServiceReplacer not available");
                    showActivationFailedNotification(project);
                    return;
                }

                // 显示安全通知
                showSecureNoAuthNotification(project);
            } else {
                LOG.error("Failed to enable Secure No-Auth mode");
                showActivationFailedNotification(project);
            }

        } catch (Exception e) {
            LOG.error("Failed to initialize Secure No-Auth mode", e);
            showErrorNotification(project, e);
        }
    }

    /**
     * 执行认证设置
     */
    private void performAuthenticationSetup(@NotNull AuthService authService) {
        try {
            // 触发认证成功事件，通知其他组件
            Object authObserverService = ApplicationManager.getApplication().getService(AuthObserver.class);
            if (authObserverService != null && authService.a() != null) {
                try {
                    // 使用安全的方法调用，避免 ClassCastException
                    AuthObserverUtils.safeAuthSuccess(authObserverService, authService.a());
                    LOG.info("Secure authentication event triggered via safe method");
                } catch (Exception e) {
                    LOG.warn("Failed to trigger authentication event safely", e);
                    // 尝试直接转换作为后备方案
                    try {
                        AuthObserver authObserver = AuthObserver.safeCast(authObserverService);
                        if (authObserver != null) {
                            authObserver.onAuthenticationSuccess(authService.a());
                            LOG.info("Secure authentication event triggered via direct cast");
                        } else {
                            LOG.warn("Could not cast to AuthObserver: " +
                                    AuthObserverUtils.getClassLoaderInfo(authObserverService));
                        }
                    } catch (Exception e2) {
                        LOG.error("All authentication event trigger methods failed", e2);
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("Failed to perform authentication setup", e);
        }
    }

    private void showSecureNoAuthNotification(@NotNull Project project) {
        ApplicationManager.getApplication().invokeLater(() -> {
            try {
                NotificationGroupManager.getInstance()
                    .getNotificationGroup("Zencoder.NoAuth")
                    .createNotification(
                        "Zencoder Secure No-Auth Extension",
                        "Secure No-Auth mode is active with safety controls. Environment verified for development use.",
                        NotificationType.WARNING
                    )
                    .notify(project);
            } catch (Exception e) {
                LOG.warn("Failed to show secure notification", e);
            }
        });
    }
    
    private void showSecurityErrorNotification(@NotNull Project project, 
                                             EnvironmentSecurityChecker.SecurityCheckResult securityCheck) {
        ApplicationManager.getApplication().invokeLater(() -> {
            try {
                String message = "Security check failed. No-Auth mode disabled.\n" +
                               "Errors: " + String.join(", ", securityCheck.getErrors());
                
                NotificationGroupManager.getInstance()
                    .getNotificationGroup("Zencoder.NoAuth")
                    .createNotification(
                        "Zencoder No-Auth Security Error",
                        message,
                        NotificationType.ERROR
                    )
                    .notify(project);
            } catch (Exception e) {
                LOG.warn("Failed to show security error notification", e);
            }
        });
    }
    
    private void showActivationFailedNotification(@NotNull Project project) {
        ApplicationManager.getApplication().invokeLater(() -> {
            try {
                NotificationGroupManager.getInstance()
                    .getNotificationGroup("Zencoder.NoAuth")
                    .createNotification(
                        "Zencoder No-Auth Activation Failed",
                        "Failed to activate No-Auth mode. Check logs for details.",
                        NotificationType.ERROR
                    )
                    .notify(project);
            } catch (Exception e) {
                LOG.warn("Failed to show activation failed notification", e);
            }
        });
    }
    
    private void showErrorNotification(@NotNull Project project, Exception error) {
        ApplicationManager.getApplication().invokeLater(() -> {
            try {
                NotificationGroupManager.getInstance()
                    .getNotificationGroup("Zencoder.NoAuth")
                    .createNotification(
                        "Zencoder No-Auth Error",
                        "Error during initialization: " + error.getMessage(),
                        NotificationType.ERROR
                    )
                    .notify(project);
            } catch (Exception e) {
                LOG.warn("Failed to show error notification", e);
            }
        });
    }
}
