package ai.zencoder.plugin.network;

import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.net.SocketTimeoutException;
import java.util.concurrent.*;
import java.util.function.Supplier;

/**
 * 网络超时处理器
 * 为网络连接问题提供超时处理和离线模式支持
 */
public class NetworkTimeoutHandler {
    
    private static final Logger LOG = Logger.getInstance(NetworkTimeoutHandler.class);
    
    // 默认超时时间（毫秒）
    private static final int DEFAULT_TIMEOUT_MS = 5000;
    private static final int QUICK_TIMEOUT_MS = 2000;
    
    // 线程池用于执行网络操作
    private static final ExecutorService NETWORK_EXECUTOR = Executors.newCachedThreadPool(r -> {
        Thread thread = new Thread(r, "Zencoder-Network-Handler");
        thread.setDaemon(true);
        return thread;
    });
    
    /**
     * 执行网络操作，带超时处理
     */
    @Nullable
    public static <T> T executeWithTimeout(@NotNull Supplier<T> networkOperation, 
                                          int timeoutMs, 
                                          @Nullable T fallbackValue) {
        try {
            Future<T> future = NETWORK_EXECUTOR.submit(networkOperation::get);
            return future.get(timeoutMs, TimeUnit.MILLISECONDS);
            
        } catch (TimeoutException e) {
            LOG.warn("Network operation timed out after " + timeoutMs + "ms, using fallback value");
            return fallbackValue;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            LOG.warn("Network operation interrupted, using fallback value");
            return fallbackValue;
            
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof SocketTimeoutException) {
                LOG.warn("Socket timeout occurred, using fallback value: " + cause.getMessage());
            } else {
                LOG.warn("Network operation failed, using fallback value", cause);
            }
            return fallbackValue;
            
        } catch (Exception e) {
            LOG.warn("Unexpected error in network operation, using fallback value", e);
            return fallbackValue;
        }
    }
    
    /**
     * 执行网络操作，使用默认超时
     */
    @Nullable
    public static <T> T executeWithTimeout(@NotNull Supplier<T> networkOperation, 
                                          @Nullable T fallbackValue) {
        return executeWithTimeout(networkOperation, DEFAULT_TIMEOUT_MS, fallbackValue);
    }
    
    /**
     * 执行快速网络操作，使用较短超时
     */
    @Nullable
    public static <T> T executeQuickWithTimeout(@NotNull Supplier<T> networkOperation, 
                                               @Nullable T fallbackValue) {
        return executeWithTimeout(networkOperation, QUICK_TIMEOUT_MS, fallbackValue);
    }
    
    /**
     * 执行网络操作，无返回值
     */
    public static void executeWithTimeout(@NotNull Runnable networkOperation, int timeoutMs) {
        executeWithTimeout(() -> {
            networkOperation.run();
            return null;
        }, timeoutMs, null);
    }
    
    /**
     * 执行网络操作，使用默认超时，无返回值
     */
    public static void executeWithTimeout(@NotNull Runnable networkOperation) {
        executeWithTimeout(networkOperation, DEFAULT_TIMEOUT_MS);
    }
    
    /**
     * 检查网络连接是否可用
     */
    public static boolean isNetworkAvailable() {
        return executeQuickWithTimeout(() -> {
            try {
                // 尝试连接到一个可靠的服务
                java.net.URL url = new java.net.URL("https://www.google.com");
                java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
                connection.setRequestMethod("HEAD");
                connection.setConnectTimeout(1000);
                connection.setReadTimeout(1000);
                connection.connect();
                return connection.getResponseCode() == 200;
            } catch (Exception e) {
                return false;
            }
        }, false);
    }
    
    /**
     * 创建离线模式的默认配置
     */
    @NotNull
    public static <T> T getOfflineDefault(@NotNull Class<T> type) {
        if (type == String.class) {
            return type.cast("");
        } else if (type == java.util.List.class) {
            return type.cast(java.util.Collections.emptyList());
        } else if (type == java.util.Map.class) {
            return type.cast(java.util.Collections.emptyMap());
        } else if (type == java.util.Set.class) {
            return type.cast(java.util.Collections.emptySet());
        } else if (type == Boolean.class || type == boolean.class) {
            return type.cast(false);
        } else if (type == Integer.class || type == int.class) {
            return type.cast(0);
        }
        
        try {
            // 尝试创建默认实例
            return type.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            LOG.warn("Cannot create default instance for type: " + type.getName());
            return null;
        }
    }
    
    /**
     * 关闭网络处理器
     */
    public static void shutdown() {
        NETWORK_EXECUTOR.shutdown();
        try {
            if (!NETWORK_EXECUTOR.awaitTermination(5, TimeUnit.SECONDS)) {
                NETWORK_EXECUTOR.shutdownNow();
            }
        } catch (InterruptedException e) {
            NETWORK_EXECUTOR.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
