package ai.zencoder.plugin.action;

import ai.zencoder.plugin.diagnostic.ZencoderDiagnostic;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.ui.Messages;
import com.intellij.openapi.util.IconLoader;
import org.jetbrains.annotations.NotNull;
import javax.swing.Icon;

/**
 * Zencoder 诊断操作
 * 提供完整的系统诊断功能
 */
public class DiagnosticAction extends AnAction {
    
    private static final Logger LOG = Logger.getInstance(DiagnosticAction.class);
    
    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        try {
            if (e.getProject() == null) {
                Messages.showErrorDialog("请在项目中运行诊断", "错误");
                return;
            }
            
            LOG.info("Running Zencoder diagnostic...");
            
            // 执行诊断
            ZencoderDiagnostic.DiagnosticResult result = ZencoderDiagnostic.performDiagnostic(e.getProject());
            
            // 显示结果
            String title = "Zencoder 系统诊断";
            String message = result.toString();

            // 根据诊断结果确定消息类型和图标
            Icon messageIcon = Messages.getInformationIcon();
            if (!result.zencoderPluginFound) {
                messageIcon = Messages.getErrorIcon();
                title = "诊断结果 - 发现问题";
            } else if (!result.zencoderPluginEnabled || !result.noAuthModeActive) {
                messageIcon = Messages.getWarningIcon();
                title = "诊断结果 - 需要注意";
            }

            Messages.showMessageDialog(e.getProject(), message, title, messageIcon);
            
            // 记录诊断结果到日志
            LOG.info("Diagnostic completed:\n" + result.toString());
            
        } catch (Exception ex) {
            LOG.error("Failed to run diagnostic", ex);
            Messages.showErrorDialog(e.getProject(), 
                "诊断执行失败: " + ex.getMessage(), 
                "诊断错误");
        }
    }
    
    @Override
    public void update(@NotNull AnActionEvent e) {
        // 只在项目打开时启用
        e.getPresentation().setEnabled(e.getProject() != null);
        e.getPresentation().setText("🔍 Zencoder 系统诊断");
        e.getPresentation().setDescription("运行完整的 Zencoder 系统诊断，检查插件状态和配置");
    }
}
