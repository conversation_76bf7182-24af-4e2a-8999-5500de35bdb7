package ai.zencoder.plugin.settings;

import ai.zencoder.plugin.network.NetworkTimeoutHandler;
import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 安全的 Zencoder 设置管理器
 * 提供网络超时保护和离线模式支持
 */
public class SafeZencoderSettingsManager {
    
    private static final Logger LOG = Logger.getInstance(SafeZencoderSettingsManager.class);
    
    // 默认设置值
    private static final Map<String, Object> DEFAULT_SETTINGS = new HashMap<>();
    
    // 设置缓存
    private static final Map<String, Object> settingsCache = new ConcurrentHashMap<>();
    
    // 初始化状态
    private static volatile boolean initialized = false;
    private static volatile boolean initializationFailed = false;
    
    static {
        initializeDefaultSettings();
    }
    
    /**
     * 初始化默认设置
     */
    private static void initializeDefaultSettings() {
        DEFAULT_SETTINGS.put("enableCodeCompletion", true);
        DEFAULT_SETTINGS.put("enableChatFeature", true);
        DEFAULT_SETTINGS.put("enableRagFeature", false); // 默认关闭 RAG 以避免网络问题
        DEFAULT_SETTINGS.put("maxTokens", 4096);
        DEFAULT_SETTINGS.put("temperature", 0.7);
        DEFAULT_SETTINGS.put("timeoutMs", 30000);
        DEFAULT_SETTINGS.put("retryCount", 3);
        DEFAULT_SETTINGS.put("offlineMode", false);
        DEFAULT_SETTINGS.put("debugMode", false);
        DEFAULT_SETTINGS.put("autoSave", true);
        DEFAULT_SETTINGS.put("theme", "default");
        DEFAULT_SETTINGS.put("language", "en");
    }
    
    /**
     * 安全初始化设置管理器
     */
    public static void safeInitialize() {
        if (initialized || initializationFailed) {
            return;
        }
        
        LOG.info("Attempting to safely initialize Zencoder settings");
        
        try {
            // 尝试加载设置，带超时保护
            Boolean success = NetworkTimeoutHandler.executeWithTimeout(
                () -> {
                    try {
                        loadSettingsFromStorage();
                        return true;
                    } catch (Exception e) {
                        LOG.warn("Failed to load settings from storage", e);
                        return false;
                    }
                },
                5000, // 5秒超时
                false
            );
            
            if (success != null && success) {
                initialized = true;
                LOG.info("Successfully initialized Zencoder settings");
            } else {
                // 加载失败，使用默认设置
                LOG.warn("Failed to load settings, using default values");
                settingsCache.putAll(DEFAULT_SETTINGS);
                initialized = true;
            }
            
        } catch (Exception e) {
            LOG.error("Critical error during settings initialization", e);
            initializationFailed = true;
            // 即使失败也要提供默认设置
            settingsCache.putAll(DEFAULT_SETTINGS);
        }
    }
    
    /**
     * 从存储加载设置
     */
    private static void loadSettingsFromStorage() {
        // 这里应该是原始的设置加载逻辑
        // 由于我们无法直接修改原始类，这里使用默认设置
        LOG.debug("Loading settings from storage...");
        
        // 模拟加载过程
        settingsCache.putAll(DEFAULT_SETTINGS);
        
        // 如果有网络连接，尝试加载远程配置
        if (NetworkTimeoutHandler.isNetworkAvailable()) {
            loadRemoteSettings();
        }
    }
    
    /**
     * 加载远程设置
     */
    private static void loadRemoteSettings() {
        NetworkTimeoutHandler.executeWithTimeout(() -> {
            try {
                LOG.debug("Attempting to load remote settings...");
                // 这里应该是远程设置加载逻辑
                // 目前只是占位符
            } catch (Exception e) {
                LOG.debug("Failed to load remote settings", e);
            }
        }, 3000);
    }
    
    /**
     * 获取设置值
     */
    @Nullable
    public static Object getSetting(@NotNull String key) {
        safeInitialize();
        return settingsCache.getOrDefault(key, DEFAULT_SETTINGS.get(key));
    }
    
    /**
     * 获取字符串设置
     */
    @NotNull
    public static String getStringSetting(@NotNull String key, @NotNull String defaultValue) {
        Object value = getSetting(key);
        return value instanceof String ? (String) value : defaultValue;
    }
    
    /**
     * 获取布尔设置
     */
    public static boolean getBooleanSetting(@NotNull String key, boolean defaultValue) {
        Object value = getSetting(key);
        return value instanceof Boolean ? (Boolean) value : defaultValue;
    }
    
    /**
     * 获取整数设置
     */
    public static int getIntSetting(@NotNull String key, int defaultValue) {
        Object value = getSetting(key);
        return value instanceof Integer ? (Integer) value : defaultValue;
    }
    
    /**
     * 获取双精度设置
     */
    public static double getDoubleSetting(@NotNull String key, double defaultValue) {
        Object value = getSetting(key);
        return value instanceof Double ? (Double) value : defaultValue;
    }
    
    /**
     * 设置值
     */
    public static void setSetting(@NotNull String key, @Nullable Object value) {
        safeInitialize();
        settingsCache.put(key, value);
        
        // 异步保存设置
        NetworkTimeoutHandler.executeWithTimeout(() -> {
            try {
                saveSettingsToStorage();
            } catch (Exception e) {
                LOG.warn("Failed to save settings", e);
            }
        }, 2000);
    }
    
    /**
     * 保存设置到存储
     */
    private static void saveSettingsToStorage() {
        LOG.debug("Saving settings to storage...");
        // 这里应该是设置保存逻辑
    }
    
    /**
     * 检查是否处于离线模式
     */
    public static boolean isOfflineMode() {
        return getBooleanSetting("offlineMode", false) || 
               !NetworkTimeoutHandler.isNetworkAvailable();
    }
    
    /**
     * 获取所有设置
     */
    @NotNull
    public static Map<String, Object> getAllSettings() {
        safeInitialize();
        return new HashMap<>(settingsCache);
    }
    
    /**
     * 重置为默认设置
     */
    public static void resetToDefaults() {
        LOG.info("Resetting settings to defaults");
        settingsCache.clear();
        settingsCache.putAll(DEFAULT_SETTINGS);
        saveSettingsToStorage();
    }
    
    /**
     * 检查初始化状态
     */
    public static boolean isInitialized() {
        return initialized;
    }
    
    /**
     * 检查初始化是否失败
     */
    public static boolean isInitializationFailed() {
        return initializationFailed;
    }
    
    /**
     * 获取设置状态信息
     */
    @NotNull
    public static String getStatusInfo() {
        return String.format(
            "Settings Status: initialized=%s, failed=%s, offline=%s, cached_settings=%d",
            initialized, initializationFailed, isOfflineMode(), settingsCache.size()
        );
    }
}
