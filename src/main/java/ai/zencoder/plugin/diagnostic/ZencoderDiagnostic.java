package ai.zencoder.plugin.diagnostic;

import ai.zencoder.plugin.noauth.AuthServiceReplacer;
import ai.zencoder.plugin.settings.SafeZencoderSettingsManager;
import ai.zencoder.plugin.network.NetworkTimeoutHandler;
import com.intellij.ide.plugins.IdeaPluginDescriptor;
import com.intellij.ide.plugins.PluginManagerCore;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.extensions.PluginId;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowManager;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

/**
 * Zencoder 诊断工具
 * 用于检查和诊断 Zencoder 插件的状态
 */
public class ZencoderDiagnostic {
    
    private static final Logger LOG = Logger.getInstance(ZencoderDiagnostic.class);
    
    /**
     * 执行完整的诊断检查
     */
    public static DiagnosticResult performDiagnostic(@NotNull Project project) {
        DiagnosticResult result = new DiagnosticResult();
        
        // 检查插件安装状态
        checkPluginInstallation(result);
        
        // 检查认证状态
        checkAuthenticationStatus(result);
        
        // 检查工具窗口状态
        checkToolWindows(project, result);

        // 检查类加载状态
        checkClassLoading(result);

        // 检查安全组件状态
        checkSafeComponents(result);
        
        return result;
    }
    
    /**
     * 检查插件安装状态
     */
    private static void checkPluginInstallation(DiagnosticResult result) {
        // 检查 Zencoder 主插件
        String[] zencoderPluginIds = {
            "ai.zencoder.plugin",
            "com.zencoder.plugin", 
            "zencoder-plugin",
            "zencoder"
        };
        
        for (String pluginId : zencoderPluginIds) {
            IdeaPluginDescriptor plugin = PluginManagerCore.getPlugin(PluginId.getId(pluginId));
            if (plugin != null) {
                result.zencoderPluginFound = true;
                result.zencoderPluginId = pluginId;
                result.zencoderPluginVersion = plugin.getVersion();
                result.zencoderPluginEnabled = plugin.isEnabled();
                break;
            }
        }
        
        // 检查当前插件
        IdeaPluginDescriptor currentPlugin = PluginManagerCore.getPlugin(PluginId.getId("ai.zencoder.plugin.noauth"));
        if (currentPlugin != null) {
            result.noAuthPluginFound = true;
            result.noAuthPluginEnabled = currentPlugin.isEnabled();
            result.noAuthPluginVersion = currentPlugin.getVersion();
        }
    }
    
    /**
     * 检查认证状态
     */
    private static void checkAuthenticationStatus(DiagnosticResult result) {
        try {
            AuthServiceReplacer replacer = ApplicationManager.getApplication().getService(AuthServiceReplacer.class);
            if (replacer != null) {
                result.authServiceReplacerAvailable = true;
                result.noAuthModeActive = replacer.isNoAuthActive();
                result.authServiceClassName = replacer.getAuthServiceClassName();
            }
        } catch (Exception e) {
            result.authCheckError = e.getMessage();
        }
    }
    
    /**
     * 检查工具窗口状态
     */
    private static void checkToolWindows(Project project, DiagnosticResult result) {
        ToolWindowManager toolWindowManager = ToolWindowManager.getInstance(project);
        
        String[] possibleWindowIds = {
            "Zencoder", "ZencoderChat", "Zencoder Chat",
            "AI Assistant", "Zencoder Assistant", "ZencoderSafeChat"
        };
        
        for (String windowId : possibleWindowIds) {
            ToolWindow toolWindow = toolWindowManager.getToolWindow(windowId);
            if (toolWindow != null) {
                result.toolWindowsFound.add(windowId);
                if (toolWindow.isVisible()) {
                    result.visibleToolWindows.add(windowId);
                }
            }
        }
    }
    
    /**
     * 检查类加载状态
     */
    private static void checkClassLoading(DiagnosticResult result) {
        String[] zencoderClasses = {
            "ai.zencoder.plugin.ZencoderPlugin",
            "ai.zencoder.plugin.auth.AuthService",
            "ai.zencoder.plugin.chat.ChatService",
            "ai.zencoder.plugin.ui.ZencoderUI"
        };
        
        for (String className : zencoderClasses) {
            try {
                Class.forName(className);
                result.loadableClasses.add(className);
            } catch (ClassNotFoundException e) {
                result.missingClasses.add(className);
            }
        }
    }

    /**
     * 检查安全组件状态
     */
    private static void checkSafeComponents(DiagnosticResult result) {
        try {
            // 检查网络状态
            result.networkAvailable = NetworkTimeoutHandler.isNetworkAvailable();

            // 检查安全设置管理器
            result.safeSettingsInitialized = SafeZencoderSettingsManager.isInitialized();
            result.safeSettingsFailed = SafeZencoderSettingsManager.isInitializationFailed();
            result.offlineMode = SafeZencoderSettingsManager.isOfflineMode();

            // 获取设置状态信息
            result.settingsStatusInfo = SafeZencoderSettingsManager.getStatusInfo();

        } catch (Exception e) {
            result.safeComponentsError = e.getMessage();
        }
    }

    /**
     * 诊断结果类
     */
    public static class DiagnosticResult {
        // 插件状态
        public boolean zencoderPluginFound = false;
        public String zencoderPluginId = null;
        public String zencoderPluginVersion = null;
        public boolean zencoderPluginEnabled = false;
        
        public boolean noAuthPluginFound = false;
        public String noAuthPluginVersion = null;
        public boolean noAuthPluginEnabled = false;
        
        // 认证状态
        public boolean authServiceReplacerAvailable = false;
        public boolean noAuthModeActive = false;
        public String authServiceClassName = null;
        public String authCheckError = null;
        
        // UI 状态
        public List<String> toolWindowsFound = new ArrayList<>();
        public List<String> visibleToolWindows = new ArrayList<>();
        
        // 类加载状态
        public List<String> loadableClasses = new ArrayList<>();
        public List<String> missingClasses = new ArrayList<>();

        // 安全组件状态
        public boolean networkAvailable = false;
        public boolean safeSettingsInitialized = false;
        public boolean safeSettingsFailed = false;
        public boolean offlineMode = false;
        public String settingsStatusInfo = null;
        public String safeComponentsError = null;
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("=== Zencoder 诊断报告 ===\n\n");
            
            sb.append("插件状态:\n");
            sb.append("- Zencoder 主插件: ").append(zencoderPluginFound ? "已找到" : "未找到").append("\n");
            if (zencoderPluginFound) {
                sb.append("  ID: ").append(zencoderPluginId).append("\n");
                sb.append("  版本: ").append(zencoderPluginVersion).append("\n");
                sb.append("  启用状态: ").append(zencoderPluginEnabled ? "已启用" : "已禁用").append("\n");
            }
            
            sb.append("- No-Auth 插件: ").append(noAuthPluginFound ? "已找到" : "未找到").append("\n");
            if (noAuthPluginFound) {
                sb.append("  版本: ").append(noAuthPluginVersion).append("\n");
                sb.append("  启用状态: ").append(noAuthPluginEnabled ? "已启用" : "已禁用").append("\n");
            }
            
            sb.append("\n认证状态:\n");
            sb.append("- AuthServiceReplacer: ").append(authServiceReplacerAvailable ? "可用" : "不可用").append("\n");
            sb.append("- No-Auth 模式: ").append(noAuthModeActive ? "激活" : "未激活").append("\n");
            if (authServiceClassName != null) {
                sb.append("- AuthService 实现: ").append(authServiceClassName).append("\n");
            }
            if (authCheckError != null) {
                sb.append("- 认证检查错误: ").append(authCheckError).append("\n");
            }
            
            sb.append("\nUI 状态:\n");
            sb.append("- 找到的工具窗口: ").append(toolWindowsFound.size()).append(" 个\n");
            for (String window : toolWindowsFound) {
                sb.append("  * ").append(window).append("\n");
            }
            sb.append("- 可见的工具窗口: ").append(visibleToolWindows.size()).append(" 个\n");
            for (String window : visibleToolWindows) {
                sb.append("  * ").append(window).append("\n");
            }
            
            sb.append("\n类加载状态:\n");
            sb.append("- 可加载的类: ").append(loadableClasses.size()).append(" 个\n");
            sb.append("- 缺失的类: ").append(missingClasses.size()).append(" 个\n");
            for (String className : missingClasses) {
                sb.append("  * ").append(className).append("\n");
            }

            sb.append("\n安全组件状态:\n");
            sb.append("- 网络连接: ").append(networkAvailable ? "可用" : "不可用").append("\n");
            sb.append("- 安全设置管理器: ").append(safeSettingsInitialized ? "已初始化" : "未初始化").append("\n");
            sb.append("- 设置初始化失败: ").append(safeSettingsFailed ? "是" : "否").append("\n");
            sb.append("- 离线模式: ").append(offlineMode ? "是" : "否").append("\n");
            if (settingsStatusInfo != null) {
                sb.append("- 设置状态: ").append(settingsStatusInfo).append("\n");
            }
            if (safeComponentsError != null) {
                sb.append("- 安全组件错误: ").append(safeComponentsError).append("\n");
            }

            return sb.toString();
        }
    }
}
