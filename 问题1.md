##日志信息：
行 5342: 2025-08-29 12:26:37,838 [6130154]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5343: 2025-08-29 12:26:37,838 [6130154]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5344: 2025-08-29 12:26:44,292 [6136608]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5345: 2025-08-29 12:26:44,292 [6136608]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5385: 2025-08-29 12:27:43,595 [6195911]   INFO - #c.i.i.p.DynamicPlugins - Plugin ai.zencoder.plugin.noauth is not unload-safe because of unresolved extension com.intellij.projectActivity
行 5401: 2025-08-29 12:27:52,187 [6204503]   INFO - #ai.zencoder.plugin.transport.mcp.McpAblyTransport - Disposing MCP Ably transport
行 5402: 2025-08-29 12:27:52,187 [6204503]   INFO - #ai.zencoder.plugin.socket.ably.AblyConnectionManager - Disposing Ably connection manager
行 5413: 2025-08-29 12:27:52,213 [6204529]   INFO - #ai.zencoder.plugin.webview.WebviewWrapper - Disposing webview
行 5414: 2025-08-29 12:27:52,218 [6204534]   INFO - #ai.zencoder.plugin.providers.completion.nextchange.DeltaBasedHistoryService - Disposing DeltaBasedHistoryService for project: augment-plugin
行 5415: 2025-08-29 12:27:52,222 [6204538]   INFO - #ai.zencoder.plugin.providers.completion.nextchange.ThreadSafeDocumentChangeTracker - Disposing DocumentChangeTracker
行 5417: 2025-08-29 12:27:52,223 [6204539]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - MCP MessageBus server closed
行 5835: 2025-08-29 12:28:00,317 [   4245]   INFO - #ai.zencoder.plugin.providers.completion.nextchange.DeltaBasedHistoryService - DeltaBasedHistoryService initialized for project: augment-plugin
行 5902: 2025-08-29 12:28:10,859 [  14787]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5903: 2025-08-29 12:28:10,995 [  14923]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5904: 2025-08-29 12:28:11,044 [  14972]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5905: 2025-08-29 12:28:11,045 [  14973]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5906: 2025-08-29 12:28:11,047 [  14975]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5907: 2025-08-29 12:28:11,048 [  14976]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5908: 2025-08-29 12:28:11,049 [  14977]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5909: 2025-08-29 12:28:11,054 [  14982]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5910: 2025-08-29 12:28:11,089 [  15017]   INFO - #ai.zencoder.plugin.chat.agentmodels.AgentModelsManager - Fetching agent models config
行 5981: 2025-08-29 12:28:12,070 [  15998]   INFO - #ai.zencoder.plugin.chat.agentmodels.AgentModelsManager - Successfully fetched models config with 9 models
行 5984: 2025-08-29 12:28:12,221 [  16149]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5985: 2025-08-29 12:28:12,254 [  16182]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - Starting MCP MessageBus server...
行 5986: 2025-08-29 12:28:12,255 [  16183]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5987: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.migration.ZencoderPatch - Patch zencoder-patch-add-custom-agent-command-name has been already applied
行 5988: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.migration.ZencoderPatch - Patch zencoder-patch-add-review-custom-agent has been already applied
行 5989: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.migration.ZencoderPatch - Patch zencoder-patch-migrate-custom-agents-to-backend has been already applied
行 5990: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.migration.ZencoderPatch - Patch zencoder-patch-migrate-credentials-to-provider-format has been already applied
行 5991: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.migration.ZencoderPatch - Patch zencoder-patch-migrate-custom-instruction has been already applied
行 5992: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.migration.ZencoderPatch - Patch zencoder-patch-generate-chat-message-id has been already applied
行 5993: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.migration.ZencoderPatch - Patch zencoder-patch-add-chat-message-raw-content has been already applied
行 5994: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.migration.ZencoderPatch - Patch zencoder-patch-chat-storage-migration has been already applied
行 5995: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.migration.ZencoderPatch - Patch zencoder-patch-migrate-repo-info-to-rules has been already applied
行 5996: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5997: 2025-08-29 12:28:12,291 [  16219]   INFO - #ai.zencoder.plugin.agents.CustomAgentsManager - Custom agents manager has been loaded
行 5998: 2025-08-29 12:28:12,291 [  16219]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6023: 2025-08-29 12:28:12,586 [  16514]   INFO - STDOUT - 12:28:12,515 |-INFO in ch.qos.logback.core.model.processor.ConversionRuleModelHandler - registering conversion word tid with class [ai.zencoder.plugin.logging.ThreadIdConverter]
行 6058: 2025-08-29 12:28:12,646 [  16574]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registering annotated MCP handlers...
行 6059: 2025-08-29 12:28:12,646 [  16574]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Running on Windows - using enhanced classpath scanning configuration
行 6060: 2025-08-29 12:28:12,646 [  16574]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Scanning for classes annotated with @Service
行 6061: 2025-08-29 12:28:12,708 [  16636]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6062: 2025-08-29 12:28:12,726 [  16654]   INFO - #ai.zencoder.plugin.agents.CustomAgentsManager - Refreshing custom agents
行 6063: 2025-08-29 12:28:12,732 [  16660]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6071: 2025-08-29 12:28:13,218 [  17146]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Found 109 classes annotated with @Service
行 6072: 2025-08-29 12:28:13,219 [  17147]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Scanning for classes annotated with @McpTool
行 6075: 2025-08-29 12:28:13,596 [  17524]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6076: 2025-08-29 12:28:13,597 [  17525]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6077: 2025-08-29 12:28:13,632 [  17560]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Found 0 classes annotated with @McpTool
行 6078: 2025-08-29 12:28:13,632 [  17560]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Adding explicitly registered service classes
行 6079: 2025-08-29 12:28:13,632 [  17560]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Added 0 explicitly registered service classes
行 6080: 2025-08-29 12:28:13,632 [  17560]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Found 109 total service classes:
行 6089: 2025-08-29 12:28:14,211 [  18139]   INFO - #ai.zencoder.plugin.flags.FeatureFlagsService - Refreshing feature flags
行 6090: 2025-08-29 12:28:14,211 [  18139]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6100: 2025-08-29 12:28:14,448 [  18376]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: list_resources
行 6103: 2025-08-29 12:28:14,461 [  18389]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: read_resource
行 6108: 2025-08-29 12:28:14,953 [  18881]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: file_search
行 6111: 2025-08-29 12:28:14,975 [  18903]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: fulltext_search
行 6113: 2025-08-29 12:28:15,141 [  19069]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Annotated MCP handlers registered successfully
行 6114: 2025-08-29 12:28:15,141 [  19069]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - MCP MessageBus server started successfully
行 6117: 2025-08-29 12:28:15,272 [  19200]   WARN - #ai.zencoder.plugin.flags.FeatureFlagsService - Failed to fetch feature flags: timeout
行 6266: 2025-08-29 12:28:42,715 [  46643]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6267: 2025-08-29 12:28:42,716 [  46644]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6268: 2025-08-29 12:28:44,558 [  48486]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6269: 2025-08-29 12:28:44,559 [  48487]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6277: 2025-08-29 12:28:50,541 [  54469]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6278: 2025-08-29 12:28:50,541 [  54469]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6294: 2025-08-29 12:29:31,127 [  95055]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - Development mode not explicitly enabled, but proceeding
行 6295: 2025-08-29 12:29:31,129 [  95057]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SecureNoAuthService initialized - Session: noauth_1756441771127_19ab54c9 at 2025-08-29T12:29:31.1288116
行 6296: 2025-08-29 12:29:31,129 [  95057]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - ================================================================================
行 6297: 2025-08-29 12:29:31,129 [  95057]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY WARNING: No-Auth mode is ACTIVE
行 6298: 2025-08-29 12:29:31,129 [  95057]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - This bypasses normal authentication and should ONLY be used for development!
行 6299: 2025-08-29 12:29:31,129 [  95057]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - Session ID: noauth_1756441771127_19ab54c9
行 6300: 2025-08-29 12:29:31,129 [  95057]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - Started at: 2025-08-29T12:29:31.1288116
行 6301: 2025-08-29 12:29:31,129 [  95057]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - ================================================================================
行 6302: 2025-08-29 12:29:31,129 [  95057]   INFO - #ai.zencoder.plugin.noauth.AuthServiceReplacer - Successfully initialized SecureNoAuthService
行 6303: 2025-08-29 12:29:31,129 [  95057]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY_AUDIT [Session: noauth_1756441771127_19ab54c9] [Op: 1/1000] GET_AUTH_INFO_OPTIONAL - Returning restricted auth info
行 6304: 2025-08-29 12:29:31,129 [  95057]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY_AUDIT [Session: noauth_1756441771127_19ab54c9] [Op: 2/1000] CHECK_AUTHENTICATION - Returning development authentication status
行 6305: 2025-08-29 12:29:31,138 [  95066]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY_AUDIT [Session: noauth_1756441771127_19ab54c9] [Op: 3/1000] GET_AUTH_INFO_OPTIONAL - Returning restricted auth info
行 6306: 2025-08-29 12:29:31,138 [  95066]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY_AUDIT [Session: noauth_1756441771127_19ab54c9] [Op: 4/1000] CHECK_AUTHENTICATION - Returning development authentication status

实际页面：
