##日志信息：
行 5342: 2025-08-29 12:26:37,838 [6130154]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5343: 2025-08-29 12:26:37,838 [6130154]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5344: 2025-08-29 12:26:44,292 [6136608]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5345: 2025-08-29 12:26:44,292 [6136608]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5385: 2025-08-29 12:27:43,595 [6195911]   INFO - #c.i.i.p.DynamicPlugins - Plugin ai.zencoder.plugin.noauth is not unload-safe because of unresolved extension com.intellij.projectActivity
行 5401: 2025-08-29 12:27:52,187 [6204503]   INFO - #ai.zencoder.plugin.transport.mcp.McpAblyTransport - Disposing MCP Ably transport
行 5402: 2025-08-29 12:27:52,187 [6204503]   INFO - #ai.zencoder.plugin.socket.ably.AblyConnectionManager - Disposing Ably connection manager
行 5413: 2025-08-29 12:27:52,213 [6204529]   INFO - #ai.zencoder.plugin.webview.WebviewWrapper - Disposing webview
行 5414: 2025-08-29 12:27:52,218 [6204534]   INFO - #ai.zencoder.plugin.providers.completion.nextchange.DeltaBasedHistoryService - Disposing DeltaBasedHistoryService for project: augment-plugin
行 5415: 2025-08-29 12:27:52,222 [6204538]   INFO - #ai.zencoder.plugin.providers.completion.nextchange.ThreadSafeDocumentChangeTracker - Disposing DocumentChangeTracker
行 5417: 2025-08-29 12:27:52,223 [6204539]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - MCP MessageBus server closed
行 5835: 2025-08-29 12:28:00,317 [   4245]   INFO - #ai.zencoder.plugin.providers.completion.nextchange.DeltaBasedHistoryService - DeltaBasedHistoryService initialized for project: augment-plugin
行 5902: 2025-08-29 12:28:10,859 [  14787]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5903: 2025-08-29 12:28:10,995 [  14923]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5904: 2025-08-29 12:28:11,044 [  14972]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5905: 2025-08-29 12:28:11,045 [  14973]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5906: 2025-08-29 12:28:11,047 [  14975]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5907: 2025-08-29 12:28:11,048 [  14976]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5908: 2025-08-29 12:28:11,049 [  14977]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5909: 2025-08-29 12:28:11,054 [  14982]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5910: 2025-08-29 12:28:11,089 [  15017]   INFO - #ai.zencoder.plugin.chat.agentmodels.AgentModelsManager - Fetching agent models config
行 5981: 2025-08-29 12:28:12,070 [  15998]   INFO - #ai.zencoder.plugin.chat.agentmodels.AgentModelsManager - Successfully fetched models config with 9 models
行 5984: 2025-08-29 12:28:12,221 [  16149]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5985: 2025-08-29 12:28:12,254 [  16182]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - Starting MCP MessageBus server...
行 5986: 2025-08-29 12:28:12,255 [  16183]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5987: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.migration.ZencoderPatch - Patch zencoder-patch-add-custom-agent-command-name has been already applied
行 5988: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.migration.ZencoderPatch - Patch zencoder-patch-add-review-custom-agent has been already applied
行 5989: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.migration.ZencoderPatch - Patch zencoder-patch-migrate-custom-agents-to-backend has been already applied
行 5990: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.migration.ZencoderPatch - Patch zencoder-patch-migrate-credentials-to-provider-format has been already applied
行 5991: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.migration.ZencoderPatch - Patch zencoder-patch-migrate-custom-instruction has been already applied
行 5992: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.migration.ZencoderPatch - Patch zencoder-patch-generate-chat-message-id has been already applied
行 5993: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.migration.ZencoderPatch - Patch zencoder-patch-add-chat-message-raw-content has been already applied
行 5994: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.migration.ZencoderPatch - Patch zencoder-patch-chat-storage-migration has been already applied
行 5995: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.migration.ZencoderPatch - Patch zencoder-patch-migrate-repo-info-to-rules has been already applied
行 5996: 2025-08-29 12:28:12,273 [  16201]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 5997: 2025-08-29 12:28:12,291 [  16219]   INFO - #ai.zencoder.plugin.agents.CustomAgentsManager - Custom agents manager has been loaded
行 5998: 2025-08-29 12:28:12,291 [  16219]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6023: 2025-08-29 12:28:12,586 [  16514]   INFO - STDOUT - 12:28:12,515 |-INFO in ch.qos.logback.core.model.processor.ConversionRuleModelHandler - registering conversion word tid with class [ai.zencoder.plugin.logging.ThreadIdConverter]
行 6058: 2025-08-29 12:28:12,646 [  16574]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registering annotated MCP handlers...
行 6059: 2025-08-29 12:28:12,646 [  16574]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Running on Windows - using enhanced classpath scanning configuration
行 6060: 2025-08-29 12:28:12,646 [  16574]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Scanning for classes annotated with @Service
行 6061: 2025-08-29 12:28:12,708 [  16636]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6062: 2025-08-29 12:28:12,726 [  16654]   INFO - #ai.zencoder.plugin.agents.CustomAgentsManager - Refreshing custom agents
行 6063: 2025-08-29 12:28:12,732 [  16660]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6071: 2025-08-29 12:28:13,218 [  17146]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Found 109 classes annotated with @Service
行 6072: 2025-08-29 12:28:13,219 [  17147]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Scanning for classes annotated with @McpTool
行 6075: 2025-08-29 12:28:13,596 [  17524]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6076: 2025-08-29 12:28:13,597 [  17525]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6077: 2025-08-29 12:28:13,632 [  17560]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Found 0 classes annotated with @McpTool
行 6078: 2025-08-29 12:28:13,632 [  17560]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Adding explicitly registered service classes
行 6079: 2025-08-29 12:28:13,632 [  17560]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Added 0 explicitly registered service classes
行 6080: 2025-08-29 12:28:13,632 [  17560]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Found 109 total service classes:
行 6089: 2025-08-29 12:28:14,211 [  18139]   INFO - #ai.zencoder.plugin.flags.FeatureFlagsService - Refreshing feature flags
行 6090: 2025-08-29 12:28:14,211 [  18139]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6100: 2025-08-29 12:28:14,448 [  18376]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: list_resources
行 6103: 2025-08-29 12:28:14,461 [  18389]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: read_resource
行 6108: 2025-08-29 12:28:14,953 [  18881]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: file_search
行 6111: 2025-08-29 12:28:14,975 [  18903]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: fulltext_search
行 6113: 2025-08-29 12:28:15,141 [  19069]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Annotated MCP handlers registered successfully
行 6114: 2025-08-29 12:28:15,141 [  19069]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - MCP MessageBus server started successfully
行 6117: 2025-08-29 12:28:15,272 [  19200]   WARN - #ai.zencoder.plugin.flags.FeatureFlagsService - Failed to fetch feature flags: timeout
行 6266: 2025-08-29 12:28:42,715 [  46643]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6267: 2025-08-29 12:28:42,716 [  46644]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6268: 2025-08-29 12:28:44,558 [  48486]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6269: 2025-08-29 12:28:44,559 [  48487]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6277: 2025-08-29 12:28:50,541 [  54469]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6278: 2025-08-29 12:28:50,541 [  54469]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 6294: 2025-08-29 12:29:31,127 [  95055]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - Development mode not explicitly enabled, but proceeding
行 6295: 2025-08-29 12:29:31,129 [  95057]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SecureNoAuthService initialized - Session: noauth_1756441771127_19ab54c9 at 2025-08-29T12:29:31.1288116
行 6296: 2025-08-29 12:29:31,129 [  95057]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - ================================================================================
行 6297: 2025-08-29 12:29:31,129 [  95057]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY WARNING: No-Auth mode is ACTIVE
行 6298: 2025-08-29 12:29:31,129 [  95057]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - This bypasses normal authentication and should ONLY be used for development!
行 6299: 2025-08-29 12:29:31,129 [  95057]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - Session ID: noauth_1756441771127_19ab54c9
行 6300: 2025-08-29 12:29:31,129 [  95057]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - Started at: 2025-08-29T12:29:31.1288116
行 6301: 2025-08-29 12:29:31,129 [  95057]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - ================================================================================
行 6302: 2025-08-29 12:29:31,129 [  95057]   INFO - #ai.zencoder.plugin.noauth.AuthServiceReplacer - Successfully initialized SecureNoAuthService
行 6303: 2025-08-29 12:29:31,129 [  95057]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY_AUDIT [Session: noauth_1756441771127_19ab54c9] [Op: 1/1000] GET_AUTH_INFO_OPTIONAL - Returning restricted auth info
行 6304: 2025-08-29 12:29:31,129 [  95057]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY_AUDIT [Session: noauth_1756441771127_19ab54c9] [Op: 2/1000] CHECK_AUTHENTICATION - Returning development authentication status
行 6305: 2025-08-29 12:29:31,138 [  95066]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY_AUDIT [Session: noauth_1756441771127_19ab54c9] [Op: 3/1000] GET_AUTH_INFO_OPTIONAL - Returning restricted auth info
行 6306: 2025-08-29 12:29:31,138 [  95066]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY_AUDIT [Session: noauth_1756441771127_19ab54c9] [Op: 4/1000] CHECK_AUTHENTICATION - Returning development authentication status

实际页面：
![img.png](img.png)
登录完成了，但是没有操作页面，输入框，对话等交互界面，请详细分析问题并总结，可执行方案并进行验证。



日志2；
2025-08-29 14:06:00,911 [  17448]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - Starting MCP MessageBus server...
2025-08-29 14:06:00,914 [  17451] SEVERE - #c.i.i.s.i.StartupManagerImpl - null [Plugin: ai.zencoder.plugin] [Plugin: ai.zencoder.plugin]
com.intellij.diagnostic.PluginException: null [Plugin: ai.zencoder.plugin] [Plugin: ai.zencoder.plugin]
at com.intellij.ide.startup.impl.StartupManagerImplKt$launchActivity$1.invokeSuspend(StartupManagerImpl.kt:499)
at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:608)
at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:873)
at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:763)
at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:750)
Caused by: com.intellij.diagnostic.PluginException: null [Plugin: ai.zencoder.plugin]
at com.intellij.serviceContainer.ServiceInstanceInitializer.createInstance$suspendImpl(ServiceInstanceInitializer.kt:52)
at com.intellij.serviceContainer.ServiceInstanceInitializer.createInstance(ServiceInstanceInitializer.kt)
at com.intellij.platform.instanceContainer.internal.LazyInstanceHolder$initialize$1$1.invokeSuspend(LazyInstanceHolder.kt:163)
at com.intellij.platform.instanceContainer.internal.LazyInstanceHolder$initialize$1$1.invoke(LazyInstanceHolder.kt)
at com.intellij.platform.instanceContainer.internal.LazyInstanceHolder$initialize$1$1.invoke(LazyInstanceHolder.kt)
at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:62)
at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
at kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
at com.intellij.platform.instanceContainer.internal.LazyInstanceHolder$initialize$1.invokeSuspend(LazyInstanceHolder.kt:161)
at com.intellij.platform.instanceContainer.internal.LazyInstanceHolder$initialize$1.invoke(LazyInstanceHolder.kt)
at com.intellij.platform.instanceContainer.internal.LazyInstanceHolder$initialize$1.invoke(LazyInstanceHolder.kt)
at kotlinx.coroutines.intrinsics.UndispatchedKt.startCoroutineUndispatched(Undispatched.kt:27)
at kotlinx.coroutines.CoroutineStart.invoke(CoroutineStart.kt:90)
at kotlinx.coroutines.AbstractCoroutine.start(AbstractCoroutine.kt:123)
at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch(Builders.common.kt:52)
at kotlinx.coroutines.BuildersKt.launch(Unknown Source)
at com.intellij.platform.instanceContainer.internal.LazyInstanceHolder.initialize(LazyInstanceHolder.kt:146)
at com.intellij.platform.instanceContainer.internal.LazyInstanceHolder.access$initialize(LazyInstanceHolder.kt:14)
at com.intellij.platform.instanceContainer.internal.LazyInstanceHolder.tryInitialize(LazyInstanceHolder.kt:136)
at com.intellij.platform.instanceContainer.internal.LazyInstanceHolder.getInstance(LazyInstanceHolder.kt:96)
at com.intellij.platform.instanceContainer.internal.LazyInstanceHolder.getInstanceInCallerContext$suspendImpl(LazyInstanceHolder.kt:88)
at com.intellij.platform.instanceContainer.internal.LazyInstanceHolder.getInstanceInCallerContext(LazyInstanceHolder.kt)
at com.intellij.serviceContainer.ComponentManagerImplKt$doGetOrCreateInstanceBlocking$1.invokeSuspend(ComponentManagerImpl.kt:1565)
at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
at kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
at kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:112)
at kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$BuildersKt__BuildersKt(Builders.kt:85)
at kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:53)
at kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
at com.intellij.serviceContainer.ComponentManagerImplKt.runBlockingInitialization$lambda$10(ComponentManagerImpl.kt:1683)
at com.intellij.openapi.progress.ContextKt.prepareThreadContext(context.kt:81)
at com.intellij.serviceContainer.ComponentManagerImplKt.runBlockingInitialization(ComponentManagerImpl.kt:1674)
at com.intellij.serviceContainer.ComponentManagerImplKt.doGetOrCreateInstanceBlocking(ComponentManagerImpl.kt:1564)
at com.intellij.serviceContainer.ComponentManagerImplKt.getOrCreateInstanceBlocking(ComponentManagerImpl.kt:1559)
at com.intellij.serviceContainer.ComponentManagerImpl.doGetService(ComponentManagerImpl.kt:752)
at com.intellij.serviceContainer.ComponentManagerImpl.getService(ComponentManagerImpl.kt:696)
at ai.zencoder.plugin.codegen.ZencoderEditorFactoryListener.editorCreated(ZencoderEditorFactoryListener.kt:97)
at com.intellij.openapi.editor.impl.EditorFactoryImpl.doCreateEditor$lambda$1(EditorFactoryImpl.kt:223)
at com.intellij.openapi.editor.impl.EditorFactoryImpl.doCreateEditor$lambda$2(EditorFactoryImpl.kt:223)
at com.intellij.openapi.extensions.ExtensionPointName.forEachExtensionSafe(ExtensionPointName.kt:61)
at com.intellij.openapi.editor.impl.EditorFactoryImpl.doCreateEditor(EditorFactoryImpl.kt:223)
at com.intellij.openapi.editor.impl.EditorFactoryImpl.createMainEditor(EditorFactoryImpl.kt:192)
at com.intellij.openapi.fileEditor.impl.text.PsiAwareTextEditorProvider$createFileEditor$2$1.invokeSuspend$lambda$1(PsiAwareTextEditorProvider.kt:89)
at com.intellij.openapi.application.CoroutinesKt.writeIntentReadAction$lambda$1$lambda$0(coroutines.kt:339)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runWriteIntentReadAction$lambda$6(AnyThreadWriteThreadingSupport.kt:274)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runWithTemporaryThreadLocal(AnyThreadWriteThreadingSupport.kt:204)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runWriteIntentReadAction(AnyThreadWriteThreadingSupport.kt:274)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runWriteIntentReadAction(AnyThreadWriteThreadingSupport.kt:222)
at com.intellij.openapi.application.impl.ApplicationImpl.runWriteIntentReadAction(ApplicationImpl.java:1009)
at com.intellij.openapi.application.CoroutinesKt.writeIntentReadAction$lambda$1(coroutines.kt:339)
at com.intellij.openapi.progress.CoroutinesKt.blockingContextInner(coroutines.kt:345)
at com.intellij.openapi.progress.CoroutinesKt$blockingContext$2.invokeSuspend(coroutines.kt:237)
at com.intellij.openapi.progress.CoroutinesKt$blockingContext$2.invoke(coroutines.kt)
at com.intellij.openapi.progress.CoroutinesKt$blockingContext$2.invoke(coroutines.kt)
at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:62)
at kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:261)
at com.intellij.openapi.progress.CoroutinesKt.blockingContext(coroutines.kt:236)
at com.intellij.openapi.application.CoroutinesKt.writeIntentReadAction(coroutines.kt:338)
at com.intellij.openapi.fileEditor.impl.text.PsiAwareTextEditorProvider$createFileEditor$2$1.invokeSuspend(PsiAwareTextEditorProvider.kt:88)
at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
at com.intellij.openapi.application.impl.EdtCoroutineDispatcher$wrapWithLocking$2$1.run(EdtCoroutineDispatcher.kt:71)
at com.intellij.openapi.application.WriteIntentReadAction.lambda$run$0(WriteIntentReadAction.java:24)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runWriteIntentReadAction$lambda$6(AnyThreadWriteThreadingSupport.kt:274)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runWithTemporaryThreadLocal(AnyThreadWriteThreadingSupport.kt:204)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runWriteIntentReadAction(AnyThreadWriteThreadingSupport.kt:274)
at com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runWriteIntentReadAction(AnyThreadWriteThreadingSupport.kt:222)
at com.intellij.openapi.application.impl.ApplicationImpl.runWriteIntentReadAction(ApplicationImpl.java:1009)
at com.intellij.openapi.application.WriteIntentReadAction.compute(WriteIntentReadAction.java:55)
at com.intellij.openapi.application.WriteIntentReadAction.run(WriteIntentReadAction.java:23)
at com.intellij.openapi.application.impl.EdtCoroutineDispatcher$wrapWithLocking$$inlined$Runnable$2.run(Runnable.kt:15)
at com.intellij.openapi.application.TransactionGuardImpl$2.run(TransactionGuardImpl.java:225)
at com.intellij.openapi.application.impl.FlushQueue.runNextEvent(FlushQueue.java:117)
at com.intellij.openapi.application.impl.FlushQueue.flushNow(FlushQueue.java:43)
at java.desktop/java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318)
at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:781)
at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:728)
at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:722)
at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87)
at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:750)
at com.intellij.ide.IdeEventQueue.defaultDispatchEvent(IdeEventQueue.kt:585)
at com.intellij.ide.IdeEventQueue._dispatchEvent(IdeEventQueue.kt:482)
at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10$lambda$9(IdeEventQueue.kt:307)
at com.intellij.openapi.progress.impl.CoreProgressManager.computePrioritized(CoreProgressManager.java:864)
at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10(IdeEventQueue.kt:306)
at com.intellij.ide.IdeEventQueueKt.performActivity$lambda$3(IdeEventQueue.kt:958)
at com.intellij.openapi.application.TransactionGuardImpl.performActivity(TransactionGuardImpl.java:109)
at com.intellij.ide.IdeEventQueueKt.performActivity(IdeEventQueue.kt:958)
at com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12(IdeEventQueue.kt:301)
at com.intellij.ide.IdeEventQueue.dispatchEvent(IdeEventQueue.kt:341)
at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:207)
at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:128)
at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:117)
at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:113)
at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:105)
at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:92)
Caused by: java.lang.ExceptionInInitializerError
at ai.zencoder.plugin.settings.ZencoderSettings.<init>(ZencoderSettings.kt:169)
at com.intellij.platform.instanceContainer.instantiation.InstantiateKt.instantiate$lambda$0(instantiate.kt:43)
at com.intellij.platform.instanceContainer.instantiation.InstantiateKt.instantiate$lambda$8$lambda$7(instantiate.kt:289)
at com.intellij.platform.instanceContainer.instantiation.InstantiateKt.withStoredTemporaryContext(instantiate.kt:306)
at com.intellij.platform.instanceContainer.instantiation.InstantiateKt.instantiate(instantiate.kt:288)
at com.intellij.platform.instanceContainer.instantiation.InstantiateKt.instantiate(instantiate.kt:40)
at com.intellij.serviceContainer.ServiceInstanceInitializer.createInstance$suspendImpl(ServiceInstanceInitializer.kt:30)
... 97 more
Caused by: org.yaml.snakeyaml.error.YAMLException: java.net.SocketTimeoutException: timeout
at org.yaml.snakeyaml.reader.StreamReader.update(StreamReader.java:214)
at org.yaml.snakeyaml.reader.StreamReader.ensureEnoughData(StreamReader.java:171)
at org.yaml.snakeyaml.reader.StreamReader.peek(StreamReader.java:131)
at org.yaml.snakeyaml.scanner.ScannerImpl.scanFlowScalarNonSpaces(ScannerImpl.java:1916)
at org.yaml.snakeyaml.scanner.ScannerImpl.scanFlowScalar(ScannerImpl.java:1896)
at org.yaml.snakeyaml.scanner.ScannerImpl.fetchFlowScalar(ScannerImpl.java:1072)
at org.yaml.snakeyaml.scanner.ScannerImpl.fetchDouble(ScannerImpl.java:1054)
at org.yaml.snakeyaml.scanner.ScannerImpl.fetchMoreTokens(ScannerImpl.java:442)
at org.yaml.snakeyaml.scanner.ScannerImpl.checkToken(ScannerImpl.java:239)
at org.yaml.snakeyaml.parser.ParserImpl$ParseIndentlessSequenceEntryValue.produce(ParserImpl.java:610)
at org.yaml.snakeyaml.parser.ParserImpl$ParseIndentlessSequenceEntryKey.produce(ParserImpl.java:592)
at org.yaml.snakeyaml.parser.ParserImpl.peekEvent(ParserImpl.java:161)
at org.yaml.snakeyaml.comments.CommentEventsCollector$1.peek(CommentEventsCollector.java:57)
at org.yaml.snakeyaml.comments.CommentEventsCollector$1.peek(CommentEventsCollector.java:43)
at org.yaml.snakeyaml.comments.CommentEventsCollector.collectEvents(CommentEventsCollector.java:136)
at org.yaml.snakeyaml.comments.CommentEventsCollector.collectEvents(CommentEventsCollector.java:116)
at org.yaml.snakeyaml.composer.Composer.composeScalarNode(Composer.java:249)
at org.yaml.snakeyaml.composer.Composer.composeNode(Composer.java:214)
at org.yaml.snakeyaml.composer.Composer.composeSequenceNode(Composer.java:284)
at org.yaml.snakeyaml.composer.Composer.composeNode(Composer.java:216)
at org.yaml.snakeyaml.composer.Composer.composeValueNode(Composer.java:396)
at org.yaml.snakeyaml.composer.Composer.composeMappingChildren(Composer.java:361)
at org.yaml.snakeyaml.composer.Composer.composeMappingNode(Composer.java:329)
at org.yaml.snakeyaml.composer.Composer.composeNode(Composer.java:218)
at org.yaml.snakeyaml.composer.Composer.composeValueNode(Composer.java:396)
at org.yaml.snakeyaml.composer.Composer.composeMappingChildren(Composer.java:361)
at org.yaml.snakeyaml.composer.Composer.composeMappingNode(Composer.java:329)
at org.yaml.snakeyaml.composer.Composer.composeNode(Composer.java:218)
at org.yaml.snakeyaml.composer.Composer.getNode(Composer.java:141)
at org.yaml.snakeyaml.composer.Composer.getSingleNode(Composer.java:167)
at org.yaml.snakeyaml.constructor.BaseConstructor.getSingleData(BaseConstructor.java:179)
at org.yaml.snakeyaml.Yaml.loadFromReader(Yaml.java:508)
at org.yaml.snakeyaml.Yaml.load(Yaml.java:449)
at ai.zencoder.plugin.rag.RagFilesFilteringHelper.a(RagFilesFilteringHelper.kt:99)
at ai.zencoder.plugin.rag.RagFilesFilteringHelper.c(RagFilesFilteringHelper.kt:78)
at ai.zencoder.plugin.rag.RagFilesFilteringHelper.b(RagFilesFilteringHelper.kt:56)
at ai.zencoder.plugin.rag.RagFilesFilteringHelper.<clinit>(RagFilesFilteringHelper.kt:15)
... 104 more
Caused by: java.net.SocketTimeoutException: timeout
at okhttp3.internal.http2.Http2Stream$StreamTimeout.newTimeoutException(Http2Stream.kt:675)
at okhttp3.internal.http2.Http2Stream$StreamTimeout.exitAndThrowIfTimedOut(Http2Stream.kt:684)
at okhttp3.internal.http2.Http2Stream$FramingSource.read(Http2Stream.kt:380)
at okhttp3.internal.connection.Exchange$ResponseBodySource.read(Exchange.kt:281)
at okio.RealBufferedSource.read(RealBufferedSource.kt:194)
at okio.RealBufferedSource.exhausted(RealBufferedSource.kt:202)
at okio.InflaterSource.refill(InflaterSource.kt:105)
at okio.InflaterSource.readOrInflate(InflaterSource.kt:69)
at okio.InflaterSource.read(InflaterSource.kt:42)
at okio.GzipSource.read(GzipSource.kt:67)
at okio.RealBufferedSource$inputStream$1.read(RealBufferedSource.kt:162)
at java.base/java.io.FilterInputStream.read(FilterInputStream.java:119)
at java.base/java.io.PushbackInputStream.read(PushbackInputStream.java:197)
at java.base/sun.nio.cs.StreamDecoder.readBytes(StreamDecoder.java:350)
at java.base/sun.nio.cs.StreamDecoder.implRead(StreamDecoder.java:393)
at java.base/sun.nio.cs.StreamDecoder.lockedRead(StreamDecoder.java:217)
at java.base/sun.nio.cs.StreamDecoder.read(StreamDecoder.java:171)
at java.base/java.io.InputStreamReader.read(InputStreamReader.java:188)
at org.yaml.snakeyaml.reader.UnicodeReader.read(UnicodeReader.java:118)
at org.yaml.snakeyaml.reader.StreamReader.update(StreamReader.java:179)
... 140 more
2025-08-29 14:06:00,937 [  17474] SEVERE - #c.i.i.s.i.StartupManagerImpl - IntelliJ IDEA 2025.1.3  Build #IC-251.26927.53
2025-08-29 14:06:00,937 [  17474] SEVERE - #c.i.i.s.i.StartupManagerImpl - JDK: 21.0.7; VM: OpenJDK 64-Bit Server VM; Vendor: JetBrains s.r.o.
2025-08-29 14:06:00,937 [  17474] SEVERE - #c.i.i.s.i.StartupManagerImpl - OS: Windows 11
2025-08-29 14:06:00,937 [  17474] SEVERE - #c.i.i.s.i.StartupManagerImpl - Plugin to blame: Zencoder: Your Mindful AI Coding Agent version: 2.12.1
2025-08-29 14:06:00,937 [  17474] SEVERE - #c.i.i.s.i.StartupManagerImpl - Last Action:
2025-08-29 14:06:00,937 [  17474]   INFO - #c.i.o.e.s.p.m.ExternalProjectsDataStorage - Load external projects data in 492 millis (read time: 487)
2025-08-29 14:06:00,939 [  17476]   INFO - STDERR - SLF4J(E): A service provider failed to instantiate:
2025-08-29 14:06:00,939 [  17476]   INFO - STDERR - org.slf4j.spi.SLF4JServiceProvider: org.slf4j.jul.JULServiceProvider not a subtype

9135: 2025-08-29 13:49:05,267 [4526857]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 9136: 2025-08-29 13:49:05,268 [4526858]   INFO - #ai.zencoder.plugin.agents.CustomAgentsManager - Refreshing custom agents
行 9137: 2025-08-29 13:49:05,268 [4526858]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 9138: 2025-08-29 13:54:06,304 [4827894]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 9139: 2025-08-29 13:54:06,304 [4827894]   INFO - #ai.zencoder.plugin.agents.CustomAgentsManager - Refreshing custom agents
行 9140: 2025-08-29 13:54:06,304 [4827894]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 9141: 2025-08-29 13:59:08,411 [5130001]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 9142: 2025-08-29 13:59:08,412 [5130002]   INFO - #ai.zencoder.plugin.agents.CustomAgentsManager - Refreshing custom agents
行 9143: 2025-08-29 13:59:08,412 [5130002]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 9144: 2025-08-29 14:03:50,494 [5412084]   INFO - #ai.zencoder.plugin.flags.FeatureFlagsService - Refreshing feature flags
行 9145: 2025-08-29 14:03:50,494 [5412084]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 9146: 2025-08-29 14:03:51,128 [5412718]   INFO - #ai.zencoder.plugin.flags.FeatureFlagsService - New flags: {enable-web-dev-agent=FeatureFlagVariant(value=on, payload=null), use-zencoder-cli-agent-runtime=FeatureFlagVariant(value=null, payload=null), enable-new-chat-manager=FeatureFlagVariant(value=on, payload=null), enable-private-deployment-mvp=FeatureFlagVariant(value=null, payload=null), plugin-banners=FeatureFlagVariant(value=on, payload=[{"button":{"label":"Learn more","type":"external_link","url":"https://docs.zencoder.ai/features/web-dev-agent"},"endDate":"2025-08-26T00:00:00Z","icon":{"attributes":{"fill":"none","height":"13","viewBox":"0 0 14 13","width":"14","xmlns":"http://www.w3.org/2000/svg"},"children":[{"attributes":{"d":"M12.407 0.590089C12.3615 0.39829 12.2423 0.232113 12.0753 0.127506C11.9082 0.0228982 11.7066 -0.0117285 11.5142 0.0311165C11.3218 0.0739615 11.154 0.190833 11.0471 0.356449C10.9402 0.522065 10.9028 0.723109 10.943 0.916089C11.308 2.55209 11.5 4.25309 11.5 6.00009C11.5 7.74709 11.308 9.44809 10.943 11.0841C10.9215 11.1802 10.9192 11.2796 10.9362 11.3767C10.9531 11.4737 10.989 11.5664 11.0418 11.6496C11.0946 11.7327 11.1632 11.8047 11.2438 11.8613C11.3244 11.9179 11.4154 11.9581 11.5115 11.9796C11.6076 12.0011 11.707 12.0034 11.8041 11.9864C11.9011 11.9695 11.9938 11.9336 12.077 11.8808C12.1601 11.828 12.2321 11.7594 12.2887 11.6788C12.3453 11.5982 12.3855 11.5072 12.407 11.4111C12.671 10.2261 12.851 9.00909 12.938 7.76709C13.259 7.5969 13.5276 7.34249 13.7149 7.03118C13.9022 6.71986 14.0011 6.36341 14.0011 6.00009C14.0011 5.63677 13.9022 5.28032 13.7149 4.969C13.5276 4.65769 13.259 4.40328 12.938 4.23309C12.8517 3.00725 12.6742 1.78955 12.407 0.590089ZM3.348 9.00009H3C2.20435 9.00009 1.44129 8.68402 0.87868 8.12141C0.31607 7.5588 0 6.79574 0 6.00009C0 5.20444 0.31607 4.44138 0.87868 3.87877C1.44129 3.31616 2.20435 3.00009 3 3.00009H5C6.647 3.00009 8.217 2.66809 9.646 2.06709C9.878 3.34109 10 4.65509 10 6.00009C10 7.34509 9.878 8.65909 9.646 9.93309C8.30412 9.36966 6.87 ...
行 9147: 2025-08-29 14:04:09,679 [5431269]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 9148: 2025-08-29 14:04:09,680 [5431270]   INFO - #ai.zencoder.plugin.agents.CustomAgentsManager - Refreshing custom agents
行 9149: 2025-08-29 14:04:09,680 [5431270]   INFO - #ai.zencoder.plugin.auth.AuthOperationsTrackerService - Loading access token from auth storage for auth.zencoder.ai, access token: eyJh..._5LQ, refresh token: 7890...fc39
行 9152: 2025-08-29 14:04:51,391 [5472981]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - Development mode not explicitly enabled, but proceeding
行 9153: 2025-08-29 14:04:51,392 [5472982]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SecureNoAuthService initialized - Session: noauth_1756447491391_1abc7c7c at 2025-08-29T14:04:51.3915912
行 9154: 2025-08-29 14:04:51,392 [5472982]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - ================================================================================
行 9155: 2025-08-29 14:04:51,392 [5472982]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY WARNING: No-Auth mode is ACTIVE
行 9156: 2025-08-29 14:04:51,392 [5472982]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - This bypasses normal authentication and should ONLY be used for development!
行 9157: 2025-08-29 14:04:51,392 [5472982]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - Session ID: noauth_1756447491391_1abc7c7c
行 9158: 2025-08-29 14:04:51,392 [5472982]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - Started at: 2025-08-29T14:04:51.3915912
行 9159: 2025-08-29 14:04:51,392 [5472982]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - ================================================================================
行 9160: 2025-08-29 14:04:51,392 [5472982]   INFO - #ai.zencoder.plugin.noauth.AuthServiceReplacer - Successfully initialized SecureNoAuthService
行 9161: 2025-08-29 14:04:51,392 [5472982]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY_AUDIT [Session: noauth_1756447491391_1abc7c7c] [Op: 1/1000] GET_AUTH_INFO_OPTIONAL - Returning restricted auth info
行 9162: 2025-08-29 14:04:51,392 [5472982]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY_AUDIT [Session: noauth_1756447491391_1abc7c7c] [Op: 2/1000] CHECK_AUTHENTICATION - Returning development authentication status
行 9163: 2025-08-29 14:04:51,402 [5472992]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY_AUDIT [Session: noauth_1756447491391_1abc7c7c] [Op: 3/1000] GET_AUTH_INFO_OPTIONAL - Returning restricted auth info
行 9164: 2025-08-29 14:04:51,402 [5472992]   WARN - #ai.zencoder.plugin.api.SecureNoAuthService - SECURITY_AUDIT [Session: noauth_1756447491391_1abc7c7c] [Op: 4/1000] CHECK_AUTHENTICATION - Returning development authentication status
行 9165: 2025-08-29 14:05:07,270 [5488860]   INFO - #c.i.i.p.DynamicPlugins - Plugin ai.zencoder.plugin.noauth is not unload-safe because of unresolved extension com.intellij.projectActivity
行 9192: 2025-08-29 14:05:39,915 [5521505]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - MCP MessageBus server closed
行 9193: 2025-08-29 14:05:39,916 [5521506]   INFO - #ai.zencoder.plugin.webview.WebviewWrapper - Disposing webview
行 9194: 2025-08-29 14:05:39,918 [5521508]   INFO - #ai.zencoder.plugin.providers.completion.nextchange.DeltaBasedHistoryService - Disposing DeltaBasedHistoryService for project: augment-plugin
行 9195: 2025-08-29 14:05:39,918 [5521508]   INFO - #ai.zencoder.plugin.providers.completion.nextchange.ThreadSafeDocumentChangeTracker - Disposing DocumentChangeTracker
行 9771: 2025-08-29 14:05:46,697 [   3234]   INFO - #ai.zencoder.plugin.providers.completion.nextchange.DeltaBasedHistoryService - DeltaBasedHistoryService initialized for project: augment-plugin
行 9777: 2025-08-29 14:05:58,390 [  14927] SEVERE - #c.i.o.e.i.ExtensionPointImpl - null [Plugin: ai.zencoder.plugin]
行 9778: com.intellij.diagnostic.PluginException: null [Plugin: ai.zencoder.plugin]
行 9816: 	at ai.zencoder.plugin.codegen.ZencoderEditorFactoryListener.editorCreated(ZencoderEditorFactoryListener.kt:97)
行 9878: 	at ai.zencoder.plugin.settings.ZencoderSettings.<init>(ZencoderSettings.kt:169)
行 9920: 	at ai.zencoder.plugin.rag.RagFilesFilteringHelper.a(RagFilesFilteringHelper.kt:99)
行 9921: 	at ai.zencoder.plugin.rag.RagFilesFilteringHelper.c(RagFilesFilteringHelper.kt:78)
行 9922: 	at ai.zencoder.plugin.rag.RagFilesFilteringHelper.b(RagFilesFilteringHelper.kt:56)
行 9923: 	at ai.zencoder.plugin.rag.RagFilesFilteringHelper.<clinit>(RagFilesFilteringHelper.kt:15)
行 9954: 2025-08-29 14:05:58,795 [  15332] SEVERE - #c.i.o.w.i.ToolWindowManagerImpl - Cannot init toolwindow ai.zencoder.plugin.webview.chat.ChatToolWindowFactory@107e7848 [Plugin: ai.zencoder.plugin]
行 9954: 2025-08-29 14:05:58,795 [  15332] SEVERE - #c.i.o.w.i.ToolWindowManagerImpl - Cannot init toolwindow ai.zencoder.plugin.webview.chat.ChatToolWindowFactory@107e7848 [Plugin: ai.zencoder.plugin]
行 9955: com.intellij.diagnostic.PluginException: Cannot init toolwindow ai.zencoder.plugin.webview.chat.ChatToolWindowFactory@107e7848 [Plugin: ai.zencoder.plugin]
行 9955: com.intellij.diagnostic.PluginException: Cannot init toolwindow ai.zencoder.plugin.webview.chat.ChatToolWindowFactory@107e7848 [Plugin: ai.zencoder.plugin]
行 10005: Caused by: com.intellij.diagnostic.PluginException: null [Plugin: ai.zencoder.plugin]
行 10043: 	at ai.zencoder.plugin.codegen.ZencoderEditorFactoryListener.editorCreated(ZencoderEditorFactoryListener.kt:97)
行 10068: 	at ai.zencoder.plugin.settings.ZencoderSettings.<init>(ZencoderSettings.kt:169)
行 10110: 	at ai.zencoder.plugin.rag.RagFilesFilteringHelper.a(RagFilesFilteringHelper.kt:99)
行 10111: 	at ai.zencoder.plugin.rag.RagFilesFilteringHelper.c(RagFilesFilteringHelper.kt:78)
行 10112: 	at ai.zencoder.plugin.rag.RagFilesFilteringHelper.b(RagFilesFilteringHelper.kt:56)
行 10113: 	at ai.zencoder.plugin.rag.RagFilesFilteringHelper.<clinit>(RagFilesFilteringHelper.kt:15)
行 10301: 2025-08-29 14:06:00,911 [  17448]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - Starting MCP MessageBus server...
行 10302: 2025-08-29 14:06:00,914 [  17451] SEVERE - #c.i.i.s.i.StartupManagerImpl - null [Plugin: ai.zencoder.plugin] [Plugin: ai.zencoder.plugin]
行 10302: 2025-08-29 14:06:00,914 [  17451] SEVERE - #c.i.i.s.i.StartupManagerImpl - null [Plugin: ai.zencoder.plugin] [Plugin: ai.zencoder.plugin]
行 10303: com.intellij.diagnostic.PluginException: null [Plugin: ai.zencoder.plugin] [Plugin: ai.zencoder.plugin]
行 10303: com.intellij.diagnostic.PluginException: null [Plugin: ai.zencoder.plugin] [Plugin: ai.zencoder.plugin]
行 10311: Caused by: com.intellij.diagnostic.PluginException: null [Plugin: ai.zencoder.plugin]
行 10349: 	at ai.zencoder.plugin.codegen.ZencoderEditorFactoryListener.editorCreated(ZencoderEditorFactoryListener.kt:97)
行 10411: 	at ai.zencoder.plugin.settings.ZencoderSettings.<init>(ZencoderSettings.kt:169)
行 10453: 	at ai.zencoder.plugin.rag.RagFilesFilteringHelper.a(RagFilesFilteringHelper.kt:99)
行 10454: 	at ai.zencoder.plugin.rag.RagFilesFilteringHelper.c(RagFilesFilteringHelper.kt:78)
行 10455: 	at ai.zencoder.plugin.rag.RagFilesFilteringHelper.b(RagFilesFilteringHelper.kt:56)
行 10456: 	at ai.zencoder.plugin.rag.RagFilesFilteringHelper.<clinit>(RagFilesFilteringHelper.kt:15)
行 10510: 2025-08-29 14:06:01,069 [  17606]   INFO - STDOUT - 14:06:01,019 |-INFO in ch.qos.logback.core.model.processor.ConversionRuleModelHandler - registering conversion word tid with class [ai.zencoder.plugin.logging.ThreadIdConverter]
行 10545: 2025-08-29 14:06:01,162 [  17699]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registering annotated MCP handlers...
行 10546: 2025-08-29 14:06:01,162 [  17699]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Running on Windows - using enhanced classpath scanning configuration
行 10547: 2025-08-29 14:06:01,162 [  17699]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Scanning for classes annotated with @Service
行 10550: 2025-08-29 14:06:01,575 [  18112]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Found 109 classes annotated with @Service
行 10551: 2025-08-29 14:06:01,575 [  18112]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Scanning for classes annotated with @McpTool
行 10616: 2025-08-29 14:06:01,781 [  18318]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Found 0 classes annotated with @McpTool
行 10617: 2025-08-29 14:06:01,781 [  18318]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Adding explicitly registered service classes
行 10618: 2025-08-29 14:06:01,781 [  18318]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Added 0 explicitly registered service classes
行 10619: 2025-08-29 14:06:01,781 [  18318]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Found 109 total service classes:
行 10625: 2025-08-29 14:06:02,034 [  18571]   INFO - #ai.zencoder.plugin.flags.FeatureFlagsService - Refreshing feature flags
行 10626: 2025-08-29 14:06:02,034 [  18571]   WARN - #ai.zencoder.plugin.flags.FeatureFlagsService - Failed to fetch feature flags: null [Plugin: ai.zencoder.plugin]
行 10626: 2025-08-29 14:06:02,034 [  18571]   WARN - #ai.zencoder.plugin.flags.FeatureFlagsService - Failed to fetch feature flags: null [Plugin: ai.zencoder.plugin]
行 10629: 2025-08-29 14:06:02,069 [  18606]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: list_resources
行 10631: 2025-08-29 14:06:02,071 [  18608]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: read_resource
行 10642: 2025-08-29 14:06:02,350 [  18887]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: file_search
行 10646: 2025-08-29 14:06:02,375 [  18912]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Registered method as tool: fulltext_search
行 10647: 2025-08-29 14:06:02,442 [  18979]   INFO - #ai.zencoder.plugin.mcp.service.McpAnnotationProcessor - Annotated MCP handlers registered successfully
行 10648: 2025-08-29 14:06:02,442 [  18979]   INFO - #ai.zencoder.plugin.mcp.service.McpMessageBusServerService - MCP MessageBus server started successfully